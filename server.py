from flask import Flask, request, jsonify, redirect, render_template_string, make_response
from werkzeug.middleware.proxy_fix import ProxyFix
from crypto_utils import CryptoUtils
import jwt, time
import requests
import json
import asyncio
import uuid
import os
from datetime import datetime, timezone, timedelta
import user_agents
import logging
import urllib.parse
from utils import Utils
VIETNAM_TZ = timezone(timedelta(hours=7))
logging.basicConfig(level=logging.DEBUG, format=
    '%(asctime)s %(levelname)s %(name)s: %(message)s', handlers=[logging.
    FileHandler('loggg.txt', encoding='utf-8'), logging.StreamHandler()])
logger = logging.getLogger('dashboard')
BASE_URL = 'https://dashboard.kingdev.sbs/'

# Cấu hình debug IP
IP_DEBUG_ENABLED = True  # Đặt False để tắt debug IP


def getRealIp(request):
    """
    Lấy IP thật của user từ các header khác nhau
    Ưu tiên theo thứ tự: CF-Connecting-IP > X-Real-IP > X-Forwarded-For > remote_addr
    """
    # Debug: Log tất cả IP headers
    debug_info = {
        'CF-Connecting-IP': request.headers.get('CF-Connecting-IP'),
        'X-Real-IP': request.headers.get('X-Real-IP'),
        'X-Forwarded-For': request.headers.get('X-Forwarded-For'),
        'HTTP_X_FORWARDED_FOR': request.environ.get('HTTP_X_FORWARDED_FOR'),
        'remote_addr': request.remote_addr
    }

    # Cloudflare IP (nếu dùng Cloudflare)
    cf_ip = request.headers.get('CF-Connecting-IP')
    if cf_ip:
        if IP_DEBUG_ENABLED:
            print(f"[IP_DEBUG] Using CF-Connecting-IP: {cf_ip.strip()} | All headers: {debug_info}")
        return cf_ip.strip()

    # X-Real-IP (nginx proxy)
    real_ip = request.headers.get('X-Real-IP')
    if real_ip:
        if IP_DEBUG_ENABLED:
            print(f"[IP_DEBUG] Using X-Real-IP: {real_ip.strip()} | All headers: {debug_info}")
        return real_ip.strip()

    # X-Forwarded-For (có thể có nhiều IP, lấy IP đầu tiên)
    forwarded_for = request.headers.get('X-Forwarded-For')
    if forwarded_for:
        final_ip = forwarded_for.split(',')[0].strip()
        if IP_DEBUG_ENABLED:
            print(f"[IP_DEBUG] Using X-Forwarded-For: {final_ip} | All headers: {debug_info}")
        return final_ip

    # HTTP_X_FORWARDED_FOR từ environ (fallback)
    environ_forwarded = request.environ.get('HTTP_X_FORWARDED_FOR')
    if environ_forwarded:
        final_ip = environ_forwarded.split(',')[0].strip()
        if IP_DEBUG_ENABLED:
            print(f"[IP_DEBUG] Using HTTP_X_FORWARDED_FOR: {final_ip} | All headers: {debug_info}")
        return final_ip

    # Cuối cùng dùng remote_addr
    final_ip = request.remote_addr or 'Unknown'
    if IP_DEBUG_ENABLED:
        print(f"[IP_DEBUG] Using remote_addr: {final_ip} | All headers: {debug_info}")
    return final_ip


def getSuccessHtml(currentUser, currentTime, userAvatar=None):
    avatarHtml = ''
    if userAvatar:
        avatarHtml = (
            f'<img src="{userAvatar}" style="width: 64px; height: 64px; border-radius: 50%; margin-bottom: 10px;">'
            )
    return f"""
<!DOCTYPE html>
<html>
<head>
    <title>King - Bot Authorization</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {{
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background: #000;
            color: #fff;
            font-family: 'JetBrains Mono', monospace;
        }}
        .logo {{
            width: 200px;
            margin-bottom: 20px;
        }}
        .text {{
            text-align: center;
            font-size: 24px;
            margin: 10px 0;
        }}
        .info {{
            font-size: 14px;
            color: #888;
            margin-top: 20px;
        }}
        .userInfo {{
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <img src="http://ltruowng.space/king.png" class="logo">
    <div class="text">Success - King Bot</div>
    <div class="text"> + 1, 25 POINT</div>
    <div class="userInfo">
        {avatarHtml}
        <div class="text">Authenticated by {currentUser}</div>
    </div>
    <div class="info">
        at {currentTime}
    </div>
</body>
</html>
"""

def processTaskAuth(token, taskType, currentIp, apiKey=None, isRedirectEndpoint=None):
    logger.debug(
        f'[{taskType.upper()}][REQ] token={token[:8]}, currentIp={currentIp}')
    print(
        f'[{taskType.upper()}] Processing token: {token[:20]}... from IP: {currentIp}'
        )
    try:
        payload = jwt.decode(token, CryptoUtils.jwtSecret, algorithms=['HS256']
            )
        decryptedData = CryptoUtils.decrypt(payload['data'])
        if Utils.isTokenUsed(token):
            print(
                f'[{taskType.upper()}] Token already used (pre-check): {token[:20]}...'
                )
            return False, {'error': 'TOKEN ALREADY USED - King Bot'}, 400
        if Utils.isTaskLinkUsedByToken(token):
            print(
                f'[{taskType.upper()}] Task link already used: {token[:20]}...'
                )
            return False, {'error': 'LINK ALREADY USED - King Bot'}, 400
        try:
            dataObj = json.loads(decryptedData)
            print(f"[DEBUG] Parsed dataObj: {type(dataObj)}, value: {dataObj}")
        except Exception as e:
            print(f"[DEBUG] JSON parse error: {e}")
            dataObj = None
        if dataObj and isinstance(dataObj, dict) and 'auth' in dataObj:
            if dataObj['auth'] == 'valid':
                authIp = dataObj.get('ip', '')
                currentIpFirst = currentIp
                logger.debug(
                    f"[{taskType.upper()}][VERIFY] userId={dataObj.get('uid')}, token={token[:8]}, authIp={authIp}, currentIp={currentIpFirst}"
                    )

                # Get redirectToken first before using it
                redirectToken = dataObj.get('redirectToken')

                # Get time variables first
                currentTime = int(time.time())
                authTime = dataObj.get('time', 0)

                # LOGIC MỚI: Sử dụng isRedirectEndpoint để phân biệt rõ ràng
                if taskType in ['linkngon', 'trafficuser']:
                    # Kiểm tra xem user đã hoàn thành task này hôm nay chưa
                    if taskType == 'linkngon':
                        isTaskCompleted = Utils.isLinkngonUsedToday(dataObj.get('uid'))
                    else:  # trafficuser
                        isTaskCompleted = Utils.isTrafficuserUsedToday(dataObj.get('uid'))

                    if isRedirectEndpoint is True:
                        # Đây là /redirect endpoint - lần đầu user click (tạo shortened URL)
                        isAuthUrlRequest = False
                        print(f"[{taskType.upper()}] REDIRECT ENDPOINT - Creating shortened URL")
                        print(f"[{taskType.upper()}] - isTaskCompleted: {isTaskCompleted}")
                        print(f"[{taskType.upper()}] - redirectToken: {redirectToken[:20] if redirectToken else 'None'}")
                    elif isRedirectEndpoint is False:
                        # Đây là /auth endpoint - user hoàn thành task từ shortened URL
                        print(f"[{taskType.upper()}] AUTH ENDPOINT - Checking conditions:")
                        print(f"[{taskType.upper()}] - isTaskCompleted: {isTaskCompleted}")
                        print(f"[{taskType.upper()}] - redirectToken: {redirectToken[:20] if redirectToken else 'None'}")
                        print(f"[{taskType.upper()}] - authTime: {authTime}, currentTime: {currentTime}")

                        if not isTaskCompleted and redirectToken is not None:
                            timeDelay = currentTime - authTime
                            minDelayRequired = 3  # Tối thiểu 3 giây
                            isAuthUrlRequest = timeDelay >= minDelayRequired
                            print(f"[{taskType.upper()}] AUTH ENDPOINT - Time delay: {timeDelay}s (min: {minDelayRequired}s), isAuthUrlRequest: {isAuthUrlRequest}")
                        else:
                            isAuthUrlRequest = False
                            print(f"[{taskType.upper()}] AUTH ENDPOINT - Task already completed or no redirectToken, isAuthUrlRequest: {isAuthUrlRequest}")
                    else:
                        # Fallback cho old logic (nếu không có isRedirectEndpoint)
                        currentToken = request.args.get('token')
                        timeDelay = currentTime - authTime
                        minDelayRequired = 3
                        isAuthUrlRequest = (redirectToken is not None and
                                          not isTaskCompleted and
                                          redirectToken != currentToken and
                                          timeDelay >= minDelayRequired)
                        print(f"[{taskType.upper()}] FALLBACK LOGIC - isAuthUrlRequest: {isAuthUrlRequest}")
                else:
                    # Cho yeumoney, giữ logic cũ
                    isAuthUrlRequest = redirectToken is not None

                # Debug log đã được tích hợp vào logic trên
                print(f"[{taskType.upper()}] Final isAuthUrlRequest: {isAuthUrlRequest}")


                # Chỉ đánh dấu redirectToken used khi thực sự hoàn thành task
                if redirectToken and isAuthUrlRequest:
                    Utils.markTokenAsUsed(redirectToken)
                    print(
                        f'[{taskType.upper()}] Marked redirect token as used: {redirectToken[:20]}...'
                        )

                # Logic xử lý token cho LinkNgon và TrafficUser
                if taskType in ['linkngon', 'trafficuser']:
                    if isAuthUrlRequest:
                        # Khi user thực sự hoàn thành task (đến authUrl), đánh dấu token used
                        if not Utils.tryUseToken(token):
                            print(
                                f'[{taskType.upper()}] Token already used: {token[:20]}...'
                                )
                            return False, {'error': 'TOKEN ALREADY USED - King Bot'
                                }, 400
                    else:
                        # Khi user chỉ mới click link lần đầu, chỉ kiểm tra không đánh dấu used
                        if Utils.isTokenUsed(token):
                            print(
                                f'[{taskType.upper()}] Token already used: {token[:20]}...'
                                )
                            return False, {'error': 'TOKEN ALREADY USED - King Bot'
                                }, 400
                        # Không gọi tryUseToken ở đây để tránh đánh dấu token used sớm
                else:
                    # Cho yeumoney, giữ logic cũ
                    if not Utils.tryUseToken(token):
                        print(
                            f'[{taskType.upper()}] Token already used: {token[:20]}...'
                            )
                        return False, {'error': 'TOKEN ALREADY USED - King Bot'
                            }, 400
                print(
                    f"[{taskType.upper()}] Processing auth data for user: {dataObj.get('uid', 'unknown')}"
                    )
                timeDiff = currentTime - authTime
                userId = dataObj.get('uid')
                ip = dataObj.get('ip', 'Unknown')
                userAgent = dataObj.get('ua', 'Unknown')
                commandUserId = dataObj.get('commandUserId')
                if commandUserId and commandUserId != userId:
                    return False, {'error': '? - King Bot'}, 400
                if not Utils.checkIpSecurity(userId, currentIp):
                    return False, {'error': 'IP Security Violation'}, 403
                suspicious = False
                if taskType == 'yeumoney':
                    suspicious = timeDiff < 1
                    if suspicious:
                        Utils.rollbackTokenUsage(token)
                        Utils.logUserActivity(userId, taskType, ip,
                            userAgent, timeDiff, suspicious)
                        print(
                            f'[{taskType.upper()}] Bypass detected, rolling back token: {token[:20]}...'
                            )
                        return False, {'error': 'WTF !! Stupid bitch'}, 400
                Utils.logUserActivity(userId, taskType, ip, userAgent,
                    timeDiff, suspicious)
                if Utils.isUserBanned(userId):
                    return False, {'error': 'User is banned'}, 403
                if taskType == 'yeumoney':
                    maxDaily = 3
                    if Utils.isYeumoneyLocked(userId, maxDaily):
                        return False, {'error':
                            f'LOCKED UNTIL TOMORROW - {maxDaily} times per day'
                            }, 403
                    progressData = Utils.getYeumoneyProgressData(userId)
                    currentProgress = progressData['PROGRESS_LEVEL'
                        ] if progressData else 0
                    newProgress = currentProgress + 1
                    Utils.addPoints(userId, 1.25)
                    Utils.incrementYeumoneyProgress(userId)
                    Utils.saveYeumoneyProgress(userId, newProgress, '')
                    Utils.markTaskLinkAsUsedByToken(token)
                    print(
                        f'[{taskType.upper()}] Successfully completed, progress updated for user: {userId} ({newProgress}/{maxDaily})'
                        )
                elif taskType == 'linkngon':
                    print(f'[{taskType.upper()}] Processing completion logic, isAuthUrlRequest: {isAuthUrlRequest}')
                    if isAuthUrlRequest:
                        # User thực sự hoàn thành task (đến authUrl từ shortened URL với đủ delay)
                        print(f'[{taskType.upper()}] Adding points and marking as used...')
                        Utils.addPoints(userId, 1.25)
                        Utils.markLinkngonUsed(userId)
                        Utils.markTaskLinkAsUsedByToken(token)
                        print(f'[{taskType.upper()}] TASK COMPLETED! User accessed authUrl from shortened URL (delay: {timeDelay if "timeDelay" in locals() else "N/A"}s).')
                    else:
                        print(f'[{taskType.upper()}] Task NOT completed - isAuthUrlRequest is False')
                        # Có thể là: tạo shortened URL, auto-request, hoặc chưa đủ delay
                        if redirectToken and not isTaskCompleted:
                            if "timeDelay" in locals() and "minDelayRequired" in locals():
                                print(f'[{taskType.upper()}] Request too fast! Delay: {timeDelay}s < {minDelayRequired}s required. Likely auto-request.')
                            else:
                                print(f'[{taskType.upper()}] Redirect endpoint - creating shortened URL.')
                        else:
                            print(f'[{taskType.upper()}] Creating shortened URL, task NOT completed yet.')
                elif taskType == 'trafficuser':
                    print(f'[{taskType.upper()}] Processing completion logic, isAuthUrlRequest: {isAuthUrlRequest}')
                    if isAuthUrlRequest:
                        # User thực sự hoàn thành task (đến authUrl từ shortened URL với đủ delay)
                        print(f'[{taskType.upper()}] Adding points and marking as used...')
                        Utils.addPoints(userId, 1.25)
                        Utils.markTrafficuserUsed(userId)
                        Utils.markTaskLinkAsUsedByToken(token)
                        print(f'[{taskType.upper()}] TASK COMPLETED! User accessed authUrl from shortened URL (delay: {timeDelay if "timeDelay" in locals() else "N/A"}s).')
                    else:
                        print(f'[{taskType.upper()}] Task NOT completed - isAuthUrlRequest is False')
                        # Có thể là: tạo shortened URL, auto-request, hoặc chưa đủ delay
                        if redirectToken and not isTaskCompleted:
                            if "timeDelay" in locals() and "minDelayRequired" in locals():
                                print(f'[{taskType.upper()}] Request too fast! Delay: {timeDelay}s < {minDelayRequired}s required. Likely auto-request.')
                            else:
                                print(f'[{taskType.upper()}] Redirect endpoint - creating shortened URL.')
                        else:
                            print(f'[{taskType.upper()}] Creating shortened URL, task NOT completed yet.')
                userInfo = getUserInfo(userId)
                currentUser = userInfo.get('displayName', 'Unknown')
                userAvatar = userInfo.get('avatar', '')
                currentTime = Utils.getFormattedDateTime() if hasattr(Utils,
                    'getFormattedDateTime') else datetime.now().strftime(
                    '%d/%m/%Y %H:%M')
                successHtml = getSuccessHtml(currentUser, currentTime,
                    userAvatar)
                return True, successHtml, 200
            return False, {'auth': 'unvalid'}, 400
        print(f'[{taskType.upper()}] Creating auth data for original token')
        ip = currentIp
        logger.debug(
            f'[{taskType.upper()}][CREATE] token={token[:8]}, ip_saved={ip}')
        userAgent = request.headers.get('User-Agent')
        currentTime = int(time.time())
        authData = {'auth': 'valid', 'uid': decryptedData, 'ip': ip, 'ua':
            userAgent, 'time': currentTime, 'redirectToken': token,
            'commandUserId': decryptedData}
        encryptedAuthData = CryptoUtils.encrypt(json.dumps(authData))
        jwtToken = CryptoUtils.createJwt(encryptedAuthData)
        # Tạo authUrl với endpoint phù hợp
        if taskType in ['linkngon', 'trafficuser']:
            authUrl = f'{BASE_URL}api/{taskType}/auth?token={jwtToken}'
        else:
            authUrl = f'{BASE_URL}api/{taskType}?token={jwtToken}'
        savedUrl = Utils.getTokenUrl(token)
        if savedUrl:
            print(
                f'[{taskType.upper()}] Using saved URL for token: {token[:20]}...'
                )
            return True, savedUrl, 302
        if taskType == 'yeumoney':
            # ✅ Debug dataObj type
            print(f"[DEBUG] dataObj type: {type(dataObj)}, value: {dataObj}")

            # ✅ Lấy userId từ dataObj để check mode
            # Fix: Check if dataObj is a dictionary before calling .get()
            if isinstance(dataObj, dict):
                userId = dataObj.get('uid')
            else:
                # If dataObj is not a dict, use decryptedData as userId
                userId = decryptedData
            print(f"[DEBUG] userId: {userId}")

            apiKey = (
                'a6f62ee08685c27576f15c65504d4126c12d384f7ecd8fc69f49aea44fdbd6a6'
                )
            apiUrl = 'https://yeumoney.com/QL_api.php'
            params = {'token': apiKey, 'format': 'json', 'url': authUrl}
            print(
                f'[{taskType.upper()}] Creating shortened URL with params: {params}'
                )
            r = requests.get(apiUrl, params=params)
            try:
                result = r.json()
                if result.get('status') == 'success':
                    shortenedUrl = result.get('shortenedUrl')
                    if shortenedUrl:
                        Utils.saveTokenUrl(token, shortenedUrl)
                        print(
                            f'[{taskType.upper()}] Saved shortened URL for token: {token[:20]}...'
                            )
                        return True, shortenedUrl, 302
            except Exception as e:
                print(f'Error creating shortened URL: {e}')
            return True, 'https://yeumoney.com/', 302
        elif taskType == 'linkngon':
            try:
                linkngonApi = (
                    f'https://kingdev.sbs/linkngonapi/shorten?url={authUrl}')
                headers = {
                    'Cookie': 'cf_clearance=jY89pqZ.K_WeyeQH1PFmKEGuZr8amor9oxWoz2rKLns-1753324142-*******-pCqQIlqjMWjXHQIVokf_t0g.qIbOnXJ2OIpMOnwd0vasDRVuPie.G6bIf8DkU9c8gP5F63Zp6b6MOVQbjrCL5Lo.yXMezXOOICMencPusxs2SO2ETdRY6sfiL75I8Hh6dTHcLnZISdG1w2nkbFzphLhzjYnUOMcaRdoq0ha1RzTppEOV5awf9xBnEUmBanR_xKpURI3wQtOEVL3LMt3lUtwsVoB6.m8ee5Ct2LNWijA'
                }
                response = requests.get(linkngonApi, headers=headers)
                result = response.json()
                if result.get('shortened'):
                    shortenedUrl = result['shortened']
                    Utils.saveTokenUrl(token, shortenedUrl)
                    print(
                        f'[{taskType.upper()}] Saved shortened URL for token: {token[:20]}...'
                        )
                    return True, shortenedUrl, 302
                else:
                    return False, {'error': 'Failed to create LinkNgon URL'
                        }, 500
            except Exception as e:
                print(f'Error creating LinkNgon URL: {e}')
                return False, {'error': f'LinkNgon API error: {str(e)}'}, 500
        elif taskType == 'trafficuser':
            try:
                trafficuserApi = 'https://kingdev.sbs/trafficuser/shorten'
                params = {'url': authUrl}
                headers = {
                    'Cookie': "cf_clearance=jY89pqZ.K_WeyeQH1PFmKEGuZr8amor9oxWoz2rKLns-1753324142-*******-pCqQIlqjMWjXHQIVokf_t0g.qIbOnXJ2OIpMOnwd0vasDRVuPie.G6bIf8DkU9c8gP5F63Zp6b6MOVQbjrCL5Lo.yXMezXOOICMencPusxs2SO2ETdRY6sfiL75I8Hh6dTHcLnZISdG1w2nkbFzphLhzjYnUOMcaRdoq0ha1RzTppEOV5awf9xBnEUmBanR_xKpURI3wQtOEVL3LMt3lUtwsVoB6.m8ee5Ct2LNWijA"
                }
                response = requests.get(trafficuserApi, params=params, headers=headers)
                result = response.json()
                if result.get('shortened'):
                    shortenedUrl = result['shortened']
                    Utils.saveTokenUrl(token, shortenedUrl)
                    print(
                        f'[{taskType.upper()}] Saved shortened URL for token: {token[:20]}...'
                        )
                    return True, shortenedUrl, 302
                else:
                    return False, {'error': 'Failed to create TrafficUser URL'
                        }, 500
            except Exception as e:
                print(f'Error creating TrafficUser URL: {e}')
                return False, {'error': f'TrafficUser API error: {str(e)}'
                    }, 500
        return False, {'error': 'Unknown task type'}, 400
    except jwt.InvalidSignatureError:
        return False, {'error': 'INVALID JWT SIGNATURE - King Bot'}, 401
    except jwt.DecodeError:
        return False, {'error': 'MALFORMED JWT - King Bot'}, 400
    except Exception as e:
        print(f'{taskType} API error: {e}')
        return False, {'error': 'UNKNOWN ERROR - King Bot', 'detail': str(e)
            }, 500


def getUserInfo(userId, userJson=None):
    if userJson:
        Utils.updateUserInfo(userId, userJson)
    user = Utils.getOrCreateUser(userId, userJson)
    return {'id': userId, 'username': user['USER_NAME'] or
        f'User{userId[:8]}', 'displayName': user['DISPLAY_NAME'] or
        f'User{userId[:8]}', 'avatar': user['AVATAR_URL']}


class FlashServer:

    def __init__(self, discordBot=None):
        self.app = Flask(__name__)
        self.app.wsgi_app = ProxyFix(self.app.wsgi_app, x_for=1, x_proto=1,
            x_host=1, x_prefix=1)
        self.discordBot = None
        self.verifyKey = 'KingBot@@@`'
        self.setupRoutes()
        logger.info('FlashServer initialized with ProxyFix.')

    def setupRoutes(self):

        @self.app.route('/')
        def dashboard():
            return self.getDashboardHtml()

        @self.app.route('/ban', methods=['POST'])
        def banUserRoute():
            data = request.get_json()
            userId = data.get('userId')
            duration = data.get('duration', 1)
            userJson = data.get('user')
            if userId:
                if userJson:
                    Utils.updateUserInfo(userId, userJson)
                banReason = 'Manual ban from dashboard'
                if duration == -1:
                    banReason = 'King la li do !'
                actualDuration = Utils.banUserCustom(userId, banReason,
                    duration)
                userInfo = getUserInfo(userId, userJson)
                return jsonify({'success': True, 'duration': actualDuration,
                    'user': userInfo})
            return jsonify({'error': 'Invalid user ID'}), 400

        @self.app.route('/unban', methods=['POST'])
        def unbanUserRoute():
            data = request.get_json()
            userId = data.get('userId')
            userJson = data.get('user')
            if userId:
                if userJson:
                    Utils.updateUserInfo(userId, userJson)
                success = Utils.unbanUser(userId)
                userInfo = getUserInfo(userId, userJson)
                return jsonify({'success': success, 'user': userInfo})
            return jsonify({'error': 'Invalid user ID'}), 400

        @self.app.route('/user-details', methods=['GET', 'POST'])
        def getUserDetails():
            if request.method == 'POST':
                reqData = request.get_json()
                userId = reqData.get('userId')
                userJson = reqData.get('user')
            else:
                userId = request.args.get('userId')
                userJson = None
            if not userId:
                return jsonify({'error': 'Missing user ID'}), 400
            if userJson:
                Utils.updateUserInfo(userId, userJson)
            userInfo = getUserInfo(userId, userJson)
            user = Utils.getOrCreateUser(userId)
            logs = Utils.getUserLogs(userId, 50)
            exchangeHistory = Utils.getUserExchangeHistory(userId, 20)
            banHistory = Utils.getUserBanHistory(userId)
            formattedLogs = []
            for log in logs:
                formattedLogs.append({'timestamp': log['CREATED_AT'].
                    strftime('%d/%m/%Y %H:%M'), 'site': log['SITE_NAME'],
                    'ip': log['IP_ADDRESS'], 'browser': log['BROWSER_INFO'],
                    'timeDiff': log['TIME_DIFF'], 'bypassDetected': log[
                    'BYPASS_DETECTED']})
            formattedExchanges = []
            for exchange in exchangeHistory:
                formattedExchanges.append({'timestamp': exchange[
                    'CREATED_AT'].strftime('%d/%m/%Y %H:%M'),
                    'pointsExchanged': exchange['POINTS_EXCHANGED'],
                    'accountsReceived': exchange['ACCOUNTS_RECEIVED'],
                    'exchangeType': exchange['EXCHANGE_TYPE'],
                    'accountText': exchange['ACCOUNT_TEXT']})
            formattedBans = []
            for ban in banHistory:
                formattedBans.append({'banTime': ban['CREATED_AT'].strftime
                    ('%d/%m/%Y %H:%M'), 'reason': ban['BAN_REASON'],
                    'duration': ban['BAN_DURATION'], 'banEnd': ban[
                    'BAN_END'].isoformat() if ban['BAN_END'] else None,
                    'active': ban['IS_ACTIVE']})
            return jsonify({'user': userInfo, 'points': {'yeumoney': user[
                'YEUMONEY_POINTS'], 'total': user['TOTAL_POINTS']}, 'logs':
                formattedLogs, 'exchangeHistory': formattedExchanges,
                'banHistory': formattedBans})

        @self.app.route('/delete-ban-history', methods=['POST'])
        def deleteBanHistory():
            data = request.get_json()
            userId = data.get('userId')
            userJson = data.get('user')
            if userId:
                if userJson:
                    Utils.updateUserInfo(userId, userJson)
                success = Utils.deleteBanHistory(userId)
                userInfo = getUserInfo(userId, userJson)
                return jsonify({'success': success, 'user': userInfo})
            return jsonify({'error': 'Invalid user ID'}), 400

        @self.app.route('/api/yeumoney', methods=['GET'])
        def yeumoneyApi():
            token = request.args.get('token')
            if not token:
                return jsonify({'error': '? - King Bot'}), 400
            currentIp = getRealIp(request)
            success, response_data, status_code = processTaskAuth(token,
                'yeumoney', currentIp)
            if success:
                if status_code == 302:
                    return redirect(response_data, code=302)
                else:
                    return response_data, status_code, {'Content-Type':
                        'text/html; charset=utf-8'}
            else:
                return jsonify(response_data), status_code

        @self.app.route('/api/linkngon/redirect', methods=['GET'])
        def linkngonRedirectApi():
            """Endpoint cho lần đầu user click link - tạo shortened URL"""
            token = request.args.get('token')
            if not token:
                return jsonify({'error': '? - King Bot'}), 400
            currentIp = getRealIp(request)
            success, response_data, status_code = processTaskAuth(token,
                'linkngon', currentIp, isRedirectEndpoint=True)
            if success:
                if status_code == 302:
                    return redirect(response_data, code=302)
                else:
                    return response_data, status_code, {'Content-Type':
                        'text/html; charset=utf-8'}
            else:
                return jsonify(response_data), status_code

        @self.app.route('/api/linkngon/auth', methods=['GET'])
        def linkngonAuthApi():
            """Endpoint cho khi user hoàn thành task từ shortened URL"""
            token = request.args.get('token')
            if not token:
                return jsonify({'error': '? - King Bot'}), 400
            currentIp = getRealIp(request)
            success, response_data, status_code = processTaskAuth(token,
                'linkngon', currentIp, isRedirectEndpoint=False)
            if success:
                if status_code == 302:
                    return redirect(response_data, code=302)
                else:
                    return response_data, status_code, {'Content-Type':
                        'text/html; charset=utf-8'}
            else:
                return jsonify(response_data), status_code

        @self.app.route('/api/trafficuser/redirect', methods=['GET'])
        def trafficuserRedirectApi():
            """Endpoint cho lần đầu user click link - tạo shortened URL"""
            token = request.args.get('token')
            if not token:
                return jsonify({'error': '? - King Bot'}), 400
            currentIp = getRealIp(request)
            success, response_data, status_code = processTaskAuth(token,
                'trafficuser', currentIp, isRedirectEndpoint=True)
            if success:
                if status_code == 302:
                    return redirect(response_data, code=302)
                else:
                    return response_data, status_code, {'Content-Type':
                        'text/html; charset=utf-8'}
            else:
                return jsonify(response_data), status_code

        @self.app.route('/api/trafficuser/auth', methods=['GET'])
        def trafficuserAuthApi():
            """Endpoint cho khi user hoàn thành task từ shortened URL"""
            token = request.args.get('token')
            if not token:
                return jsonify({'error': '? - King Bot'}), 400
            currentIp = getRealIp(request)
            success, response_data, status_code = processTaskAuth(token,
                'trafficuser', currentIp, isRedirectEndpoint=False)
            if success:
                if status_code == 302:
                    return redirect(response_data, code=302)
                else:
                    return response_data, status_code, {'Content-Type':
                        'text/html; charset=utf-8'}
            else:
                return jsonify(response_data), status_code

        @self.app.route('/verify', methods=['GET', 'POST'])
        def verify():
            error = None
            if request.method == 'POST':
                key = request.form.get('key', '')
                logger.debug(f'Verify POST with key: {key}')
                if key == self.verifyKey:
                    logger.info(f'Dashboard access granted for key: {key}')
                    resp = make_response(redirect('/'))
                    resp.set_cookie('Author', self.verifyKey, max_age=60 * 
                        60 * 24 * 7)
                    return resp
                else:
                    logger.warning(f'Dashboard access denied for key: {key}')
                    error = 'Sai key! Vui lòng thử lại.'
            return render_template_string(
                """
                <!DOCTYPE html>
                <html lang="vi">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Dashboard Verification</title>
                    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;700&family=Montserrat:wght@600;900&display=swap" rel="stylesheet">
                    <style>
                        body {
                            min-height: 100vh;
                            margin: 0;
                            padding: 0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            background: linear-gradient(135deg, #232526 0%, #414345 100%);
                            font-family: 'Montserrat', 'JetBrains Mono', monospace;
                        }
                        .verifyContainer {
                            background: rgba(30, 32, 40, 0.95);
                            border-radius: 24px;
                            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
                            padding: 48px 36px 36px 36px;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            min-width: 340px;
                            max-width: 90vw;
                        }
                        .verifyLogo {
                            width: 72px;
                            height: 72px;
                            border-radius: 16px;
                            margin-bottom: 18px;
                            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
                        }
                        .verifyTitle {
                            font-size: 2.2rem;
                            font-weight: 900;
                            color: #fff;
                            margin-bottom: 8px;
                            letter-spacing: 1px;
                            text-shadow: 0 2px 8px #0004;
                        }
                        .verifyDesc {
                            color: #b0b3b8;
                            font-size: 1.05rem;
                            margin-bottom: 28px;
                            text-align: center;
                        }
                        .verifyForm {
                            width: 100%;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                        }
                        .verifyInput {
                            width: 100%;
                            padding: 14px 18px;
                            font-size: 1.1rem;
                            border-radius: 12px;
                            border: none;
                            outline: none;
                            background: #23272f;
                            color: #fff;
                            margin-bottom: 18px;
                            box-shadow: 0 2px 8px #0002;
                            transition: background 0.2s;
                        }
                        .verifyInput:focus {
                            background: #2d313a;
                        }
                        .verifyBtn {
                            width: 100%;
                            padding: 14px 0;
                            font-size: 1.1rem;
                            font-weight: 700;
                            border-radius: 12px;
                            border: none;
                            background: linear-gradient(90deg, #00c6ff 0%, #0072ff 100%);
                            color: #fff;
                            cursor: pointer;
                            box-shadow: 0 2px 8px #0072ff44;
                            transition: background 0.2s, transform 0.1s;
                        }
                        .verifyBtn:hover {
                            background: linear-gradient(90deg, #0072ff 0%, #00c6ff 100%);
                            transform: translateY(-2px) scale(1.03);
                        }
                        .verifyError {
                            margin-top: 18px;
                            color: #ff4d4f;
                            background: #2d1a1a;
                            border-radius: 8px;
                            padding: 10px 18px;
                            font-size: 1.05rem;
                            font-weight: 600;
                            box-shadow: 0 2px 8px #ff4d4f22;
                            letter-spacing: 0.5px;
                        }
                        @media (max-width: 500px) {
                            .verifyContainer {
                                padding: 28px 8vw 24px 8vw;
                                min-width: unset;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="verifyContainer">
                        <img src="http://ltruowng.space/king.png" class="verifyLogo" alt="Logo" />
                        <div class="verifyTitle">Dashboard Access</div>
                        <div class="verifyDesc">Nhập <b>key truy cập</b> để xác thực quyền vào dashboard.<br>Liên hệ admin nếu bạn chưa có key.</div>
                        <form class="verifyForm" method="post">
                            <input class="verifyInput" type="password" name="key" placeholder="Nhập key truy cập..." required autocomplete="off" />
                            <button class="verifyBtn" type="submit">Xác thực</button>
                        </form>
                        {% if error %}<div class="verifyError">{{ error }}</div>{% endif %}
                    </div>
                </body>
                </html>
            """
                , error=error)

        @self.app.before_request
        def requireVerify():
            if request.endpoint in ('verify', 'static'):
                return None
            if request.path.startswith('/api/') or request.path.startswith(
                '/user-details'):
                return None
            authorCookie = request.cookies.get('Author')
            if authorCookie != self.verifyKey:
                logger.debug(
                    f'Require verify: missing/invalid Author cookie: {authorCookie}'
                    )
                return redirect('/verify')
            logger.debug(f'Require verify: valid Author cookie: {authorCookie}'
                )
            return None

    def getDashboardHtml(self):
        logger.info('Dashboard accessed.')
        allUsers = Utils.getAllUsers()
        users = []
        for user in allUsers:
            try:
                userId = user['USER_ID']
                logs = Utils.getUserLogs(userId, 10)
                formattedLogs = []
                for log in logs:
                    formattedLogs.append({'timestamp': log['CREATED_AT'].
                        strftime('%d/%m/%Y %H:%M'), 'site': log['SITE_NAME'
                        ], 'ip': log['IP_ADDRESS'], 'browser': log[
                        'BROWSER_INFO'], 'timeDiff': log['TIME_DIFF'],
                        'bypassDetected': log['BYPASS_DETECTED']})
                bypassDetected = any(log['BYPASS_DETECTED'] for log in logs)
                banned = Utils.isUserBanned(userId)
                exchangeHistory = Utils.getUserExchangeHistory(userId, 5)
                formattedExchanges = []
                for exchange in exchangeHistory:
                    formattedExchanges.append({'timestamp': exchange[
                        'CREATED_AT'].strftime('%d/%m/%Y %H:%M'),
                        'pointsExchanged': exchange['POINTS_EXCHANGED'],
                        'accountsReceived': exchange['ACCOUNTS_RECEIVED'],
                        'exchangeType': exchange['EXCHANGE_TYPE'],
                        'accountText': exchange['ACCOUNT_TEXT']})
                totalExchanges = len(exchangeHistory)
                totalAccountsReceived = sum(exchange['ACCOUNTS_RECEIVED'] for
                    exchange in exchangeHistory)
                userData = {'id': userId, 'username': user['USER_NAME'] or
                    f'User{userId[:8]}', 'displayName': user['DISPLAY_NAME'
                    ] or f'User{userId[:8]}', 'avatar': user['AVATAR_URL'],
                    'yeumoneyPoints': user['YEUMONEY_POINTS'],
                    'site2sPoints': 0, 'totalPoints': user['TOTAL_POINTS'],
                    'logs': formattedLogs, 'suspicious': bypassDetected,
                    'banned': banned, 'exchangeHistory': formattedExchanges,
                    'totalExchanges': totalExchanges,
                    'totalAccountsReceived': totalAccountsReceived,
                    'totalTasks': 0, 'totalTaskAccounts': 0}
                users.append(userData)
            except Exception as e:
                print(f"Error processing user {user['USER_ID']}: {e}")
        users.sort(key=lambda x: (not x['suspicious'], -x['totalPoints']))
        bannedUserIds = Utils.getBannedUsers()
        bannedUsers = []
        for userId in bannedUserIds:
            try:
                user = Utils.getOrCreateUser(userId)
                if not user:
                    continue
                banHistory = Utils.getUserBanHistory(userId)
                if banHistory:
                    lastBan = banHistory[0]
                    duration = lastBan['BAN_DURATION']
                    if duration == -1:
                        durationText = 'Vĩnh viễn'
                        status = 'Bị cấm vĩnh viễn'
                    else:
                        durationText = f'{duration} ngày'
                        if lastBan['BAN_END']:
                            remaining = lastBan['BAN_END'
                                ] - Utils.getCurrentTime()
                            if remaining.total_seconds() > 0:
                                days = remaining.days
                                hours = remaining.seconds // 3600
                                status = f'Còn {days} ngày {hours} giờ'
                            else:
                                status = 'Đã hết hạn'
                        else:
                            status = 'Đang hoạt động'
                    formattedBanHistory = []
                    for ban in banHistory[-5:]:
                        formattedBanHistory.append({'banTime': ban[
                            'CREATED_AT'].strftime('%d/%m/%Y %H:%M'),
                            'reason': ban['BAN_REASON'], 'duration': 
                            f"{ban['BAN_DURATION']} ngày" if ban[
                            'BAN_DURATION'] != -1 else 'Vĩnh viễn'})
                    bannedUsers.append({'id': userId, 'username': user[
                        'USER_NAME'] or f'User{userId[:8]}', 'displayName':
                        user['DISPLAY_NAME'] or f'User{userId[:8]}',
                        'avatar': user['AVATAR_URL'], 'banCount': len(
                        banHistory), 'duration': durationText, 'reason':
                        lastBan['BAN_REASON'], 'status': status,
                        'banHistory': formattedBanHistory, 'allBanHistory':
                        banHistory})
            except Exception as e:
                print(f'Error processing banned user {userId}: {e}')
        stats = Utils.getStats()
        exchangeUsers = [u for u in users if u['totalPoints'] > 0]
        return render_template_string(self.getDashboardTemplate(), users=
            users, exchangeUsers=exchangeUsers, bannedUsers=bannedUsers,
            totalUsers=stats['totalUsers'], suspiciousCount=stats[
            'suspiciousCount'], bannedCount=stats['bannedCount'],
            totalPoints=stats['totalPoints'], totalTasks=stats['totalTasks'
            ], totalExchanges=stats['totalExchanges'])

    def getDashboardTemplate(self):
        try:
            with open('index.html', 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            return 'Dashboard template not found!'

    def run(self):
        logger.info('Flask server running on 0.0.0.0:2503')
        self.app.run(host='0.0.0.0', port=2503)


app = Flask(__name__)
flashServer = FlashServer()
app = flashServer.app
if __name__ == '__main__':
    server = FlashServer()
    server.run()
