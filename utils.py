from database import db, VIETNAM_TZ
from datetime import datetime, timedelta
import user_agents
import logging
import requests
import time

DEBUG_MODE = 1

def debugLog(message):
    if DEBUG_MODE == 1:
        with open("debug.log", "a", encoding="utf-8") as f:
            f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}\n")

logger = logging.getLogger(__name__)

class Utils:
    @staticmethod
    def getCurrentTime():
        return datetime.now(VIETNAM_TZ)

    @staticmethod
    def getFormattedDate():
        return Utils.getCurrentTime().strftime("%Y-%m-%d")
        
    @staticmethod
    def getFormattedDateTime():
        return Utils.getCurrentTime().strftime("%d/%m/%Y %H:%M")
    
    @staticmethod
    def getCurrentWeek():
        now = Utils.getCurrentTime()
        return f"{now.year}-{now.isocalendar()[1]}"

    @staticmethod
    def getOrCreateUser(userId, userInfo=None):
        debugLog(f"getOrCreateUser called for {userId}")
        query = "SELECT * FROM USERS_DATA WHERE USER_ID = %s"
        result = db.executeQuery(query, (userId,), fetch=True)
        
        if not result:
            insertQuery = '''
                INSERT INTO USERS_DATA (USER_ID, USER_NAME, DISPLAY_NAME, AVATAR_URL) 
                VALUES (%s, %s, %s, %s)
            '''
            userName = userInfo.get('username', f'User{userId[:8]}') if userInfo else f'User{userId[:8]}'
            displayName = userInfo.get('displayName', userName) if userInfo else userName
            avatarUrl = userInfo.get('avatar') if userInfo else None
            
            db.executeQuery(insertQuery, (userId, userName, displayName, avatarUrl))
            result = db.executeQuery(query, (userId,), fetch=True)
        
        return result[0] if result else None

    @staticmethod
    def updateUserInfo(userId, userInfo):
        debugLog(f"updateUserInfo called for {userId}")
        query = '''
            UPDATE USERS_DATA 
            SET USER_NAME = %s, DISPLAY_NAME = %s, AVATAR_URL = %s, UPDATED_AT = CURRENT_TIMESTAMP
            WHERE USER_ID = %s
        '''
        db.executeQuery(query, (
            userInfo.get('username'),
            userInfo.get('displayName'),
            userInfo.get('avatar'),
            userId
        ))

    @staticmethod
    def checkIpSecurity(userId, currentIp):
        debugLog(f"checkIpSecurity: {userId}, {currentIp}")
        
        query = "SELECT REGISTERED_IP FROM USERS_DATA WHERE USER_ID = %s"
        result = db.executeQuery(query, (userId,), fetch=True)
        
        if not result or not result[0]['REGISTERED_IP']:
            updateQuery = "UPDATE USERS_DATA SET REGISTERED_IP = %s WHERE USER_ID = %s"
            db.executeQuery(updateQuery, (currentIp, userId))
            return True
        
        registeredIp = result[0]['REGISTERED_IP']
        
        if registeredIp == currentIp:
            return True
        
        checkQuery = "SELECT USER_ID FROM USERS_DATA WHERE REGISTERED_IP = %s AND USER_ID != %s"
        conflictResult = db.executeQuery(checkQuery, (currentIp, userId), fetch=True)
        
        if conflictResult:
            return False
        
        updateQuery = "UPDATE USERS_DATA SET REGISTERED_IP = %s WHERE USER_ID = %s"
        db.executeQuery(updateQuery, (currentIp, userId))
        return True

    @staticmethod
    def addPoints(userId, points):
        debugLog(f"addPoints: {userId}, points={points}")
        query = '''
            UPDATE USERS_DATA 
            SET YEUMONEY_POINTS = YEUMONEY_POINTS + %s,
                TOTAL_POINTS = TOTAL_POINTS + %s
            WHERE USER_ID = %s
        '''
        db.executeQuery(query, (points, points, userId))

    @staticmethod
    def getYeumoneyMode(userId):
        query = "SELECT YEUMONEY_MODE FROM USERS_DATA WHERE USER_ID = %s"
        result = db.executeQuery(query, (userId,), fetch=True)
        return result[0]['YEUMONEY_MODE'] if result and result[0]['YEUMONEY_MODE'] else 1

    @staticmethod
    def setYeumoneyMode(userId, mode):
        debugLog(f"setYeumoneyMode: {userId}, mode={mode}")
        query = "UPDATE USERS_DATA SET YEUMONEY_MODE = %s WHERE USER_ID = %s"
        db.executeQuery(query, (mode, userId))

    @staticmethod
    def setYeumoneyModeForAll(mode):
        debugLog(f"setYeumoneyModeForAll: mode={mode}")
        query = "UPDATE USERS_DATA SET YEUMONEY_MODE = %s"
        result = db.executeQuery(query, (mode,))

        countQuery = "SELECT COUNT(*) as total FROM USERS_DATA WHERE YEUMONEY_MODE = %s"
        countResult = db.executeQuery(countQuery, (mode,), fetch=True)
        totalUpdated = countResult[0]['total'] if countResult else 0

        debugLog(f"setYeumoneyModeForAll: Updated {totalUpdated} users to mode {mode}")
        return totalUpdated

    @staticmethod
    def isLinkngonUsedToday(userId):
        today = Utils.getFormattedDate()
        query = "SELECT 1 FROM DAILY_TASKS WHERE USER_ID = %s AND TASK_TYPE = 'linkngon' AND DATE(CREATED_AT) = %s"
        result = db.executeQuery(query, (userId, today), fetch=True)
        return len(result) > 0

    @staticmethod
    def markLinkngonUsed(userId):
        debugLog(f"markLinkngonUsed: {userId}")
        query = "INSERT INTO DAILY_TASKS (USER_ID, TASK_TYPE) VALUES (%s, 'linkngon')"
        db.executeQuery(query, (userId,))

    @staticmethod
    def isTrafficuserUsedToday(userId):
        today = Utils.getFormattedDate()
        query = "SELECT 1 FROM DAILY_TASKS WHERE USER_ID = %s AND TASK_TYPE = 'trafficuser' AND DATE(CREATED_AT) = %s"
        result = db.executeQuery(query, (userId, today), fetch=True)
        return len(result) > 0

    @staticmethod
    def markTrafficuserUsed(userId):
        debugLog(f"markTrafficuserUsed: {userId}")
        query = "INSERT INTO DAILY_TASKS (USER_ID, TASK_TYPE) VALUES (%s, 'trafficuser')"
        db.executeQuery(query, (userId,))

    @staticmethod
    def logUserActivity(userId, site, ip, userAgent, timeDiff, suspicious=False):
        debugLog(f"logUserActivity: {userId}, {site}, suspicious={suspicious}")
        try:
            ua = user_agents.parse(userAgent)
            browser = f"{ua.browser.family} {ua.browser.version_string}"
        except:
            browser = "Unknown"
        
        query = '''
            INSERT INTO USER_LOGS (USER_ID, SITE_NAME, IP_ADDRESS, USER_AGENT, BROWSER_INFO, TIME_DIFF, IS_SUSPICIOUS, BYPASS_DETECTED)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        '''
        db.executeQuery(query, (userId, site, ip, userAgent, browser, timeDiff, suspicious, suspicious))
        
        cleanupQuery = '''
            DELETE FROM USER_LOGS 
            WHERE USER_ID = %s AND LOG_ID NOT IN (
                SELECT LOG_ID FROM (
                    SELECT LOG_ID FROM USER_LOGS 
                    WHERE USER_ID = %s 
                    ORDER BY CREATED_AT DESC 
                    LIMIT 50
                ) AS recent_logs
            )
        '''
        try:
            db.executeQuery(cleanupQuery, (userId, userId))
        except:
            pass

    @staticmethod
    def banUser(userId, reason="Bypass detected"):
        debugLog(f"banUser called for {userId}")
        countQuery = "SELECT COUNT(*) as BAN_COUNT FROM BANNED_USERS WHERE USER_ID = %s"
        result = db.executeQuery(countQuery, (userId,), fetch=True)
        banCount = result[0]['BAN_COUNT'] + 1 if result else 1
        
        if banCount == 1:
            duration = 1
        elif banCount == 2:
            duration = 3
        elif banCount == 3:
            duration = 7
        elif banCount == 4:
            duration = 30
        else:
            duration = -1
        
        banEnd = None if duration == -1 else Utils.getCurrentTime() + timedelta(days=duration)
        
        query = '''
            INSERT INTO BANNED_USERS (USER_ID, BAN_COUNT, BAN_REASON, BAN_DURATION, BAN_END, IS_ACTIVE)
            VALUES (%s, %s, %s, %s, %s, %s)
        '''
        db.executeQuery(query, (userId, banCount, reason, duration, banEnd, True))
        return duration

    @staticmethod
    def banUserCustom(userId, reason="Manual ban", duration=1):
        debugLog(f"banUserCustom called for {userId}, duration={duration}")
        banEnd = None if duration == -1 else Utils.getCurrentTime() + timedelta(days=duration)
        
        query = '''
            INSERT INTO BANNED_USERS (USER_ID, BAN_COUNT, BAN_REASON, BAN_DURATION, BAN_END, IS_ACTIVE)
            VALUES (%s, %s, %s, %s, %s, %s)
        '''
        db.executeQuery(query, (userId, 1, reason, duration, banEnd, True))
        return duration

    @staticmethod
    def isUserBanned(userId):
        debugLog(f"isUserBanned called for {userId}")
        query = '''
            SELECT * FROM BANNED_USERS 
            WHERE USER_ID = %s AND IS_ACTIVE = TRUE 
            ORDER BY CREATED_AT DESC LIMIT 1
        '''
        result = db.executeQuery(query, (userId,), fetch=True)
        
        if not result:
            return False
        
        ban = result[0]
        if ban['BAN_END'] is None:
            return True
        banEnd = ban['BAN_END']
        if banEnd.tzinfo is None or banEnd.tzinfo.utcoffset(banEnd) is None:
            from database import VIETNAM_TZ
            banEnd = banEnd.replace(tzinfo=VIETNAM_TZ)
        return Utils.getCurrentTime() < banEnd

    @staticmethod
    def unbanUser(userId):
        debugLog(f"unbanUser called for {userId}")
        query = '''
            UPDATE BANNED_USERS 
            SET IS_ACTIVE = FALSE 
            WHERE USER_ID = %s AND IS_ACTIVE = TRUE
        '''
        return db.executeQuery(query, (userId,)) > 0

    @staticmethod
    def deleteBanHistory(userId):
        debugLog(f"deleteBanHistory called for {userId}")
        query = "DELETE FROM BANNED_USERS WHERE USER_ID = %s"
        return db.executeQuery(query, (userId,)) > 0

    @staticmethod
    def getYeumoneyProgress(userId):
        debugLog(f"getYeumoneyProgress called for {userId}")
        user = Utils.getOrCreateUser(userId)
        today = Utils.getFormattedDate()
        
        if user['UPDATED_AT'].date() != datetime.now().date():
            query = "UPDATE USERS_DATA SET YEUMONEY_DAILY_COUNT = 0 WHERE USER_ID = %s"
            db.executeQuery(query, (userId,))
            return 0
        
        return user['YEUMONEY_DAILY_COUNT']

    @staticmethod
    def incrementYeumoneyProgress(userId):
        debugLog(f"incrementYeumoneyProgress called for {userId}")
        query = '''
            UPDATE USERS_DATA 
            SET YEUMONEY_DAILY_COUNT = YEUMONEY_DAILY_COUNT + 1
            WHERE USER_ID = %s
        '''
        db.executeQuery(query, (userId,))
        
        user = Utils.getOrCreateUser(userId)
        return user['YEUMONEY_DAILY_COUNT']

    @staticmethod
    def isYeumoneyLocked(userId, maxDaily=3):
        progressData = Utils.getYeumoneyProgressData(userId)
        if not progressData:
            return False
        return progressData['PROGRESS_LEVEL'] >= maxDaily

    @staticmethod
    def getTotalPoints(userId):
        debugLog(f"getTotalPoints called for {userId}")
        user = Utils.getOrCreateUser(userId)
        return user['TOTAL_POINTS']

    @staticmethod
    def logExchangeActivity(userId, pointsExchanged, accountsReceived, exchangeType="auto", accountText=None):
        debugLog(f"logExchangeActivity: {userId}, points={pointsExchanged}")
        query = '''
            INSERT INTO EXCHANGE_HISTORY (USER_ID, POINTS_EXCHANGED, ACCOUNTS_RECEIVED, EXCHANGE_TYPE, ACCOUNT_TEXT)
            VALUES (%s, %s, %s, %s, %s)
        '''
        db.executeQuery(query, (userId, pointsExchanged, accountsReceived, exchangeType, accountText or ""))
        
        cleanupQuery = '''
            DELETE FROM EXCHANGE_HISTORY 
            WHERE USER_ID = %s AND EXCHANGE_ID NOT IN (
                SELECT EXCHANGE_ID FROM (
                    SELECT EXCHANGE_ID FROM EXCHANGE_HISTORY 
                    WHERE USER_ID = %s 
                    ORDER BY CREATED_AT DESC 
                    LIMIT 20
                ) AS recent_exchanges
            )
        '''
        try:
            db.executeQuery(cleanupQuery, (userId, userId))
        except:
            pass

    @staticmethod
    def exchangePointsUgphone(userId, pointsToExchange):
        debugLog(f"exchangePointsUgphone: {userId}, points={pointsToExchange}")
        user = Utils.getOrCreateUser(userId)
        totalPoints = user['TOTAL_POINTS']
        
        if totalPoints < pointsToExchange:
            return False, f"Không đủ điểm! Bạn có {totalPoints} điểm, cần {pointsToExchange} điểm để đổi.", None
        
        apiUrl = "https://shop.kingdev.sbs/api/InfoResource.php"
        params = {
            'username': 'trz.developer',
            'password': 'QuanNhjCaBuCuAnhTruong',
            'id': '19'
        }
        
        try:
            response = requests.get(apiUrl, params=params)
            print(response.text)
            result = response.json()
            
            if result.get('status') != 'success':
                return False, "Lỗi khi kiểm tra tài nguyên!", None
            
            data = result.get('data', {})
            amount = int(data.get('amount', 0))
            if amount == 0:
                return False, "Hết tài khoản! Vui lòng thử lại sau.", None
            
            buyParams = {
                'username': 'trz.developer',
                'password': 'QuanNhjCaBuCuAnhTruong',
                'id': '19',
                'amount': str(pointsToExchange)
            }
            
            buyResponse = requests.get("https://shop.kingdev.sbs/api/BResource.php", params=buyParams)
            print(buyResponse.text)
            buyResult = buyResponse.json()
            
            if buyResult.get('status') == 'success':
                accounts = buyResult.get('data', {}).get('lists', [])
                accountList = []
                for acc in accounts:
                    accountList.append(acc.get('account', ''))
                accountText = '\n'.join(accountList)
                
                query = '''
                    UPDATE USERS_DATA 
                    SET TOTAL_POINTS = TOTAL_POINTS - %s
                    WHERE USER_ID = %s
                '''
                db.executeQuery(query, (pointsToExchange, userId))
                
                Utils.logExchangeActivity(userId, pointsToExchange, pointsToExchange, "ugphone", accountText)
                
                return True, f"Đổi thành công! Đã trừ {pointsToExchange} điểm và nhận {pointsToExchange} tài khoản Ugphone.", accountText
            else:
                return False, "Lỗi khi mua tài khoản!", None
                
        except Exception as e:
            return False, f"Lỗi kết nối API: {str(e)}", None

    @staticmethod
    def exchangePointsRedfinger(userId, pointsToExchange):
        debugLog(f"exchangePointsRedfinger: {userId}, points={pointsToExchange}")
        
        if pointsToExchange % 3 != 0:
            return False, "Redfinger chỉ đổi theo bội số của 3 điểm!", None
        
        accountsToGive = pointsToExchange // 3
        user = Utils.getOrCreateUser(userId)
        totalPoints = user['TOTAL_POINTS']
        
        if totalPoints < pointsToExchange:
            return False, f"Không đủ điểm! Bạn có {totalPoints} điểm, cần {pointsToExchange} điểm để đổi.", None
        
        apiUrl = "https://shop.kingdev.sbs/api/InfoResource.php"
        params = {
            'username': 'trz.developer',
            'password': 'QuanNhjCaBuCuAnhTruong',
            'id': '20'
        }
        
        try:
            response = requests.get(apiUrl, params=params)
            result = response.json()
            
            if result.get('status') != 'success':
                return False, "Lỗi khi kiểm tra tài nguyên!", None
            data = result.get('data', {})
            try:
                amount = int(data.get('amount', 0))
            except (ValueError, TypeError):
                amount = 0

            if amount < accountsToGive:
                return False, f"[ Redfinger ] ``` Đã hết tài khoản trong kho ```", None

            buyParams = {
                'username': 'trz.developer',
                'password': 'QuanNhjCaBuCuAnhTruong',
                'id': '20',
                'amount': str(accountsToGive)
            }
            
            buyResponse = requests.get("https://shop.kingdev.sbs/api/BResource.php", params=buyParams)
            buyResult = buyResponse.json()
            
            if buyResult.get('status') == 'success':
                accounts = buyResult.get('data', {}).get('lists', [])
                accountList = []
                for acc in accounts:
                    accountList.append(acc.get('account', ''))
                accountText = '\n'.join(accountList)
                
                query = '''
                    UPDATE USERS_DATA 
                    SET TOTAL_POINTS = TOTAL_POINTS - %s
                    WHERE USER_ID = %s
                '''
                db.executeQuery(query, (pointsToExchange, userId))
                
                Utils.logExchangeActivity(userId, pointsToExchange, accountsToGive, "redfinger", accountText)
                
                return True, f"Đổi thành công! Đã trừ {pointsToExchange} điểm và nhận {accountsToGive} tài khoản Redfinger.", accountText
            else:
                return False, "Lỗi khi mua tài khoản!", None
                
        except Exception as e:
            return False, f"Lỗi kết nối API: {str(e)}", None

    @staticmethod
    def exchangePointsEmail(userId, pointsToExchange):
        debugLog(f"exchangePointsEmail: {userId}, points={pointsToExchange}")
        
        if pointsToExchange < 0.75:
            return False, "Cần ít nhất 0.75 điểm để đổi email!", None
        
        emailsToGive = int((pointsToExchange / 0.75) * 3)
        user = Utils.getOrCreateUser(userId)
        totalPoints = user['TOTAL_POINTS']
        
        if totalPoints < pointsToExchange:
            return False, f"Không đủ điểm! Bạn có {totalPoints} điểm, cần {pointsToExchange} điểm để đổi.", None
        
        try:
            stockUrl = "https://taphoammo.net/api/getStock"
            stockParams = {
                'kioskToken': 'JSYPAXDKPHRLR6T8DN2W',
                'userToken': 'R461H90FV2Q6Q2TMRKW34KAQ3VFTNM8H61L7'
            }
            
            stockResponse = requests.get(stockUrl, params=stockParams)
            
            try:
                stockResult = stockResponse.json()
            except:
                print(f"[EMAIL] Stock response not JSON: {stockResponse.text}")
                return False, "Lỗi parse response từ stock API!", None
            
            if not stockResult.get('success'):
                return False, f"Lỗi khi kiểm tra kho email: {stockResult.get('description', 'Unknown error')}", None
            
            availableStock = int(stockResult.get('stock', 0))
            if availableStock < emailsToGive:
                return False, f"Không đủ email! Cần {emailsToGive}, còn {availableStock}.", None
            
            buyUrl = "https://taphoammo.net/api/buyProducts"
            buyParams = {
                'kioskToken': 'JSYPAXDKPHRLR6T8DN2W',
                'userToken': 'R461H90FV2Q6Q2TMRKW34KAQ3VFTNM8H61L7',
                'quantity': str(emailsToGive)
            }
            
            buyResponse = requests.get(buyUrl, params=buyParams)
            
            try:
                buyResult = buyResponse.json()
            except:
                print(f"[EMAIL] Buy response not JSON: {buyResponse.text}")
                return False, "Lỗi parse response từ buy API!", None
            
            if not buyResult.get('success'):
                return False, f"Lỗi khi mua email: {buyResult.get('description', 'Unknown error')}", None
            
            orderId = buyResult.get('order_id')
            if not orderId:
                return False, "Không nhận được order_id từ API!", None
            
            print(f"[EMAIL] Order created: {orderId}, getting products...")
            
            getUrl = "https://taphoammo.net/api/getProducts"
            getParams = {
                'orderId': orderId,
                'userToken': 'R461H90FV2Q6Q2TMRKW34KAQ3VFTNM8H61L7'
            }
            
            maxRetries = 15
            for attempt in range(maxRetries):
                print(f"[EMAIL] Attempt {attempt + 1}/{maxRetries} to get products...")
                getResponse = requests.get(getUrl, params=getParams)
                
                try:
                    getResult = getResponse.json()
                except:
                    print(f"[EMAIL] Get response not JSON: {getResponse.text}")
                    if attempt < maxRetries - 1:
                        time.sleep(3)
                        continue
                    return False, "Lỗi parse response từ get products API!", None
                
                print(f"[EMAIL] Get result: {getResult}")
                
                if getResult.get('success'):
                    products = getResult.get('data', [])
                    if not products:
                        print(f"[EMAIL] No products in successful response")
                        if attempt < maxRetries - 1:
                            time.sleep(3)
                            continue
                        return False, "Không nhận được sản phẩm từ API!", None
                    
                    emailList = []
                    for product in products:
                        email = product.get('product', '')
                        if email:
                            emailList.append(email)
                    
                    if not emailList:
                        print(f"[EMAIL] No valid emails found in products")
                        if attempt < maxRetries - 1:
                            time.sleep(3)
                            continue
                        return False, "Không tìm thấy email hợp lệ!", None
                    
                    emailText = '\n'.join(emailList)
                    print(f"[EMAIL] Successfully got {len(emailList)} emails")
                    
                    query = '''
                        UPDATE USERS_DATA 
                        SET TOTAL_POINTS = TOTAL_POINTS - %s
                        WHERE USER_ID = %s
                    '''
                    db.executeQuery(query, (pointsToExchange, userId))
                    
                    Utils.logExchangeActivity(userId, pointsToExchange, len(emailList), "email", emailText)
                    
                    return True, f"Đổi thành công! Đã trừ {pointsToExchange} điểm và nhận {len(emailList)} email.", emailText
                
                elif getResult.get('description') == "Order in processing!":
                    print(f"[EMAIL] Order still processing, waiting...")
                    time.sleep(3)
                    continue
                else:
                    error_desc = getResult.get('description', 'Unknown error')
                    print(f"[EMAIL] API error: {error_desc}")
                    return False, f"Lỗi khi lấy email: {error_desc}", None
            
            return False, "Timeout khi lấy email sau 15 lần thử, vui lòng liên hệ admin!", None
            
        except Exception as e:
            print(f"[EMAIL] Exception: {str(e)}")
            return False, f"Lỗi kết nối API: {str(e)}", None

    @staticmethod
    def lockChannel(channelId):
        debugLog(f"lockChannel called for {channelId}")
        query = '''
            INSERT INTO LOCKED_CHANNELS (CHANNEL_ID, LOCKED_BY) 
            VALUES (%s, %s)
            ON DUPLICATE KEY UPDATE LOCKED_BY = %s
        '''
        db.executeQuery(query, (str(channelId), "Admin", "Admin"))
        return True

    @staticmethod
    def unlockChannel(channelId):
        debugLog(f"unlockChannel called for {channelId}")
        query = "DELETE FROM LOCKED_CHANNELS WHERE CHANNEL_ID = %s"
        return db.executeQuery(query, (str(channelId),)) > 0

    @staticmethod
    def isChannelLocked(channelId):
        query = "SELECT 1 FROM LOCKED_CHANNELS WHERE CHANNEL_ID = %s"
        result = db.executeQuery(query, (str(channelId),), fetch=True)
        return len(result) > 0

    @staticmethod
    def addTokenUsed(token):
        debugLog(f"addTokenUsed called")
        query = '''
            INSERT INTO TOKEN_DATA (TOKEN_VALUE, TOKEN_TYPE) 
            VALUES (%s, 'used')
            ON DUPLICATE KEY UPDATE TOKEN_TYPE = 'used'
        '''
        db.executeQuery(query, (token,))

    @staticmethod
    def isTokenUsed(token):
        query = "SELECT 1 FROM TOKEN_DATA WHERE TOKEN_VALUE = %s AND TOKEN_TYPE = 'used'"
        result = db.executeQuery(query, (token,), fetch=True)
        return len(result) > 0

    @staticmethod
    def tryUseToken(token):
        debugLog(f"tryUseToken called")
        query = '''
            INSERT INTO TOKEN_DATA (TOKEN_VALUE, TOKEN_TYPE)
            VALUES (%s, 'used')
            ON DUPLICATE KEY UPDATE TOKEN_TYPE = TOKEN_TYPE
        '''
        affected = db.executeQuery(query, (token,))
        return affected == 1

    @staticmethod
    def rollbackTokenUsage(token):
        debugLog(f"rollbackTokenUsage called for token")
        try:
            query = "DELETE FROM TOKEN_DATA WHERE TOKEN_VALUE = %s AND TOKEN_TYPE = 'used'"
            db.executeQuery(query, (token,))
            print(f"[ROLLBACK] Successfully rolled back token usage: {token[:20]}...")
            return True
        except Exception as e:
            print(f"Error rolling back token usage: {e}")
            return False

    @staticmethod
    def saveTokenUrl(token, url):
        debugLog(f"saveTokenUrl called")
        query = '''
            INSERT INTO TOKEN_DATA (TOKEN_VALUE, TOKEN_TYPE, URL_VALUE) 
            VALUES (%s, 'url', %s)
            ON DUPLICATE KEY UPDATE URL_VALUE = %s, TOKEN_TYPE = 'url'
        '''
        db.executeQuery(query, (token, url, url))

    @staticmethod
    def getTokenUrl(token):
        query = "SELECT URL_VALUE FROM TOKEN_DATA WHERE TOKEN_VALUE = %s AND TOKEN_TYPE = 'url'"
        result = db.executeQuery(query, (token,), fetch=True)
        return result[0]['URL_VALUE'] if result else None

    @staticmethod
    def markTokenAsUsed(token):
        debugLog(f"markTokenAsUsed called")
        query_delete = '''
            DELETE FROM TOKEN_DATA 
            WHERE TOKEN_VALUE = %s AND TOKEN_TYPE = 'url'
        '''
        db.executeQuery(query_delete, (token,))
        query_insert = '''
            INSERT IGNORE INTO TOKEN_DATA (TOKEN_VALUE, TOKEN_TYPE) VALUES (%s, 'used')
        '''
        db.executeQuery(query_insert, (token,))

    @staticmethod
    def getCurrentDay():
        return Utils.getCurrentTime().strftime("%Y-%m-%d")

    @staticmethod
    def saveYeumoneyProgress(userId, progress, redirectToken):
        debugLog(f"saveYeumoneyProgress: {userId}, progress={progress}")
        day = Utils.getCurrentDay()
        query = '''
            INSERT INTO YEUMONEY_PROGRESS (USER_ID, DAY_KEY, PROGRESS_LEVEL, REDIRECT_TOKEN, IS_LOCKED)
            VALUES (%s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE PROGRESS_LEVEL = %s, REDIRECT_TOKEN = %s
        '''
        maxDaily = 3
        isLocked = 1 if progress >= maxDaily else 0
        db.executeQuery(query, (userId, day, progress, redirectToken, isLocked, progress, redirectToken))

    @staticmethod
    def getYeumoneyProgressData(userId):
        debugLog(f"getYeumoneyProgressData called for {userId}")
        day = Utils.getCurrentDay()
        query = "SELECT * FROM YEUMONEY_PROGRESS WHERE USER_ID = %s AND DAY_KEY = %s"
        result = db.executeQuery(query, (userId, day), fetch=True)
        return result[0] if result else None

    @staticmethod
    def saveTaskLink(userId, taskType, link, progress):
        debugLog(f"saveTaskLink: {userId}, task={taskType}, progress={progress}")
        day = Utils.getCurrentDay()
        query = '''
            INSERT INTO TASK_LINKS (USER_ID, TASK_TYPE, TASK_LINK, DAY_KEY, PROGRESS_MILESTONE)
            VALUES (%s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE TASK_LINK = %s
        '''
        db.executeQuery(query, (userId, taskType, link, day, progress, link))

    @staticmethod
    def getTaskLink(userId, taskType, progress):
        day = Utils.getCurrentDay()
        query = "SELECT TASK_LINK FROM TASK_LINKS WHERE USER_ID = %s AND TASK_TYPE = %s AND DAY_KEY = %s AND PROGRESS_MILESTONE = %s AND IS_USED = FALSE"
        result = db.executeQuery(query, (userId, taskType, day, progress), fetch=True)
        return result[0]['TASK_LINK'] if result else None

    @staticmethod
    def markTaskLinkAsUsed(userId, taskType, progress):
        """Đánh dấu task link đã được sử dụng - đánh dấu TẤT CẢ link cũ của user cho task này trong ngày"""
        debugLog(f"markTaskLinkAsUsed: {userId}, task={taskType}, progress={progress}")
        day = Utils.getCurrentDay()
        query = '''
            UPDATE TASK_LINKS
            SET IS_USED = TRUE
            WHERE USER_ID = %s AND TASK_TYPE = %s AND DAY_KEY = %s AND PROGRESS_MILESTONE <= %s
        '''
        db.executeQuery(query, (userId, taskType, day, progress))

    @staticmethod
    def markTaskLinkAsUsedByToken(token):
        """Đánh dấu task link đã được sử dụng dựa trên token trong link"""
        debugLog(f"markTaskLinkAsUsedByToken: {token[:20]}...")
        query = '''
            UPDATE TASK_LINKS
            SET IS_USED = TRUE
            WHERE TASK_LINK LIKE %s
        '''
        db.executeQuery(query, (f'%token={token}%',))

    @staticmethod
    def isTaskLinkUsedByToken(token):
        """Kiểm tra xem task link có đã được sử dụng hay không dựa trên token"""
        debugLog(f"isTaskLinkUsedByToken: {token[:20]}...")
        query = '''
            SELECT 1 FROM TASK_LINKS
            WHERE TASK_LINK LIKE %s AND IS_USED = TRUE
            LIMIT 1
        '''
        result = db.executeQuery(query, (f'%token={token}%',), fetch=True)
        return len(result) > 0

    @staticmethod
    def addDmQueue(userId, content, filePath=None):
        debugLog(f"addDmQueue called for {userId}")
        query = '''
            INSERT INTO DM_QUEUE (USER_ID, MESSAGE_CONTENT, FILE_PATH)
            VALUES (%s, %s, %s)
        '''
        db.executeQuery(query, (userId, content, filePath))

    @staticmethod
    def getDmQueue():
        query = "SELECT * FROM DM_QUEUE WHERE IS_PROCESSED = FALSE ORDER BY CREATED_AT ASC"
        return db.executeQuery(query, fetch=True)

    @staticmethod
    def getDmQueueByUser(userId):
        query = "SELECT * FROM DM_QUEUE WHERE USER_ID = %s ORDER BY CREATED_AT DESC"
        return db.executeQuery(query, (userId,), fetch=True)

    @staticmethod
    def markDmProcessed(dmId):
        debugLog(f"markDmProcessed called for {dmId}")
        query = "UPDATE DM_QUEUE SET IS_PROCESSED = TRUE WHERE QUEUE_ID = %s"
        db.executeQuery(query, (dmId,))

    @staticmethod
    def getAllUsers():
        query = "SELECT * FROM USERS_DATA ORDER BY TOTAL_POINTS DESC"
        return db.executeQuery(query, fetch=True)

    @staticmethod
    def getUserLogs(userId, limit=10):
        query = '''
            SELECT * FROM USER_LOGS 
            WHERE USER_ID = %s 
            ORDER BY CREATED_AT DESC 
            LIMIT %s
        '''
        return db.executeQuery(query, (userId, limit), fetch=True)

    @staticmethod
    def getUserExchangeHistory(userId, limit=5):
        query = '''
            SELECT * FROM EXCHANGE_HISTORY 
            WHERE USER_ID = %s 
            ORDER BY CREATED_AT DESC 
            LIMIT %s
        '''
        return db.executeQuery(query, (userId, limit), fetch=True)

    @staticmethod
    def getUserBanHistory(userId):
        query = '''
            SELECT * FROM BANNED_USERS 
            WHERE USER_ID = %s 
            ORDER BY CREATED_AT DESC
        '''
        return db.executeQuery(query, (userId,), fetch=True)

    @staticmethod
    def getBannedUsers():
        query = '''
            SELECT DISTINCT bu.USER_ID 
            FROM BANNED_USERS bu
            WHERE bu.IS_ACTIVE = TRUE 
               OR (bu.BAN_END IS NOT NULL AND bu.BAN_END > NOW()) 
               OR bu.BAN_END IS NULL
        '''
        result = db.executeQuery(query, fetch=True)
        return [row['USER_ID'] for row in result]

    @staticmethod
    def getStats():
        stats = {}
        
        query = "SELECT COUNT(*) as count FROM USERS_DATA"
        result = db.executeQuery(query, fetch=True)
        stats['totalUsers'] = result[0]['count']
        
        query = "SELECT COUNT(DISTINCT USER_ID) as count FROM USER_LOGS WHERE BYPASS_DETECTED = TRUE"
        result = db.executeQuery(query, fetch=True)
        stats['suspiciousCount'] = result[0]['count']
        
        query = "SELECT COUNT(DISTINCT USER_ID) as count FROM BANNED_USERS WHERE IS_ACTIVE = TRUE OR BAN_END > NOW() OR BAN_END IS NULL"
        result = db.executeQuery(query, fetch=True)
        stats['bannedCount'] = result[0]['count']
        
        query = "SELECT SUM(TOTAL_POINTS) as total FROM USERS_DATA"
        result = db.executeQuery(query, fetch=True)
        stats['totalPoints'] = result[0]['total'] or 0
        
        query = "SELECT COUNT(*) as count FROM TASK_HISTORY"
        result = db.executeQuery(query, fetch=True)
        stats['totalTasks'] = result[0]['count']
        
        query = "SELECT COUNT(*) as count FROM EXCHANGE_HISTORY"
        result = db.executeQuery(query, fetch=True)
        stats['totalExchanges'] = result[0]['count']
        
        return stats

    @staticmethod
    def addAdmin(userId):
        debugLog(f"addAdmin called for {userId}")
        query = '''
            INSERT INTO BOT_ADMINS (USER_ID) 
            VALUES (%s)
            ON DUPLICATE KEY UPDATE USER_ID = %s
        '''
        db.executeQuery(query, (userId, userId))
        return True

    @staticmethod
    def isAdmin(userId):
        query = "SELECT 1 FROM BOT_ADMINS WHERE USER_ID = %s"
        result = db.executeQuery(query, (userId,), fetch=True)
        return len(result) > 0

    @staticmethod
    def isCTV(userId):
        """Kiểm tra user có phải CTV không"""
        query = "SELECT 1 FROM CTV_USERS WHERE USER_ID = %s"
        result = db.executeQuery(query, (userId,), fetch=True)
        return len(result) > 0

    @staticmethod
    def addCTV(userId):
        """Thêm user vào danh sách CTV"""
        debugLog(f"addCTV: {userId}")
        query = "INSERT IGNORE INTO CTV_USERS (USER_ID) VALUES (%s)"
        db.executeQuery(query, (userId,))

    @staticmethod
    def removeCTV(userId):
        """Xóa user khỏi danh sách CTV"""
        debugLog(f"removeCTV: {userId}")
        query = "DELETE FROM CTV_USERS WHERE USER_ID = %s"
        db.executeQuery(query, (userId,))

    @staticmethod
    def getCTVPointsUsedToday(ctvUserId):
        """Lấy số point CTV đã add hôm nay"""
        today = Utils.getFormattedDate()
        query = """
            SELECT COALESCE(SUM(POINTS_ADDED), 0) as total
            FROM CTV_POINT_LOGS
            WHERE CTV_USER_ID = %s AND DATE(CREATED_AT) = %s
        """
        result = db.executeQuery(query, (ctvUserId, today), fetch=True)
        return result[0]['total'] if result else 0

    @staticmethod
    def addPointsByCTV(ctvUserId, targetUserId, points):
        """CTV add points cho user khác (tối đa 10 points/người/ngày)"""
        debugLog(f"addPointsByCTV: CTV={ctvUserId}, target={targetUserId}, points={points}")

        # Kiểm tra CTV đã add bao nhiêu point cho user này hôm nay
        today = Utils.getFormattedDate()
        query = """
            SELECT COALESCE(SUM(POINTS_ADDED), 0) as total
            FROM CTV_POINT_LOGS
            WHERE CTV_USER_ID = %s AND TARGET_USER_ID = %s AND DATE(CREATED_AT) = %s
        """
        result = db.executeQuery(query, (ctvUserId, targetUserId, today), fetch=True)
        usedToday = result[0]['total'] if result else 0

        if usedToday + points > 10:
            return False, f"Vượt quá giới hạn! Đã add {usedToday}/10 points cho user này hôm nay"

        # Add points cho user
        Utils.addPoints(targetUserId, points)

        # Log lại việc add points
        logQuery = """
            INSERT INTO CTV_POINT_LOGS (CTV_USER_ID, TARGET_USER_ID, POINTS_ADDED)
            VALUES (%s, %s, %s)
        """
        db.executeQuery(logQuery, (ctvUserId, targetUserId, points))

        return True, f"Đã add {points} points cho user thành công!"

    @staticmethod
    def getResetCoinSchedule():
        """Lấy lịch reset coin"""
        query = "SELECT * FROM RESET_COIN_SCHEDULE LIMIT 1"
        result = db.executeQuery(query, fetch=True)
        if result:
            return {
                'enabled': bool(result[0]['ENABLED']),
                'day_of_week': result[0]['DAY_OF_WEEK'],
                'day_name': ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'][result[0]['DAY_OF_WEEK']]
            }
        return {'enabled': False, 'day_of_week': 1, 'day_name': 'Thứ 2'}

    @staticmethod
    def setResetCoinSchedule(enabled, dayOfWeek=None):
        """Set lịch reset coin"""
        debugLog(f"setResetCoinSchedule: enabled={enabled}, dayOfWeek={dayOfWeek}")

        if enabled and dayOfWeek is not None:
            query = """
                INSERT INTO RESET_COIN_SCHEDULE (ID, ENABLED, DAY_OF_WEEK)
                VALUES (1, %s, %s)
                ON DUPLICATE KEY UPDATE ENABLED = %s, DAY_OF_WEEK = %s
            """
            db.executeQuery(query, (enabled, dayOfWeek, enabled, dayOfWeek))
        else:
            query = """
                INSERT INTO RESET_COIN_SCHEDULE (ID, ENABLED, DAY_OF_WEEK)
                VALUES (1, %s, 1)
                ON DUPLICATE KEY UPDATE ENABLED = %s
            """
            db.executeQuery(query, (enabled, enabled))

    @staticmethod
    def resetAllCoins():
        """Reset tất cả coin của toàn bộ user"""
        debugLog("resetAllCoins called")
        query = """
            UPDATE USERS_DATA
            SET YEUMONEY_POINTS = 0, TOTAL_POINTS = 0
        """
        result = db.executeQuery(query)

        # Đếm số user được reset
        countQuery = "SELECT COUNT(*) as total FROM USERS_DATA"
        countResult = db.executeQuery(countQuery, fetch=True)
        totalReset = countResult[0]['total'] if countResult else 0

        debugLog(f"resetAllCoins: Reset coins for {totalReset} users")
        return totalReset

    @staticmethod
    def resetWeeklyProgress():
        debugLog("resetWeeklyProgress called")
        query = "UPDATE YEUMONEY_PROGRESS SET IS_LOCKED = 0 WHERE IS_LOCKED = 1"
        db.executeQuery(query)
        return True

    @staticmethod
    def resetYeumoneyDailyProgress():
        debugLog("resetYeumoneyDailyProgress called")
        today = Utils.getCurrentDay()
        query = "DELETE FROM YEUMONEY_PROGRESS WHERE DAY_KEY != %s"
        db.executeQuery(query, (today,))
        query2 = "UPDATE USERS_DATA SET YEUMONEY_DAILY_COUNT = 0"
        db.executeQuery(query2)
        query3 = "DELETE FROM DAILY_TASKS WHERE DATE(CREATED_AT) != %s"
        db.executeQuery(query3, (today,))
        return True
