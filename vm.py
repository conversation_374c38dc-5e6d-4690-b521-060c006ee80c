import ast
import base64
import marshal
import zlib
import sys
import random
import os
import ctypes

def antiDebug():
    if sys.gettrace() or ctypes.windll.kernel32.IsDebuggerPresent():
        exit()

def compileSourceToBytecode(sourceCode):
    tree = ast.parse(sourceCode)
    bytecode = []
    ctx = {}

    class VMCompiler(ast.NodeVisitor):
        def visit_Import(self, node):
            for alias in node.names:
                name = alias.asname if alias.asname else alias.name.split('.')[0]
                bytecode.append(["IMPORT", alias.name, name])

        def visit_ImportFrom(self, node):
            module = node.module if node.module else ""
            for alias in node.names:
                name = alias.asname if alias.asname else alias.name
                bytecode.append(["IMPORT_FROM", module, alias.name, name])

        def visit_Assign(self, node):
            self.visit(node.value)
            for target in node.targets:
                if isinstance(target, ast.Name):
                    bytecode.append(["STORE_NAME", target.id])
                elif isinstance(target, ast.Tuple) or isinstance(target, ast.List):
                    bytecode.append(["UNPACK_SEQUENCE", len(target.elts)])
                    for elt in reversed(target.elts):
                        if isinstance(elt, ast.Name):
                            bytecode.append(["STORE_NAME", elt.id])

        def visit_Expr(self, node):
            self.visit(node.value)
            bytecode.append(["POP_TOP"])

        def visit_Call(self, node):
            self.visit(node.func)
            for arg in node.args:
                self.visit(arg)
            for keyword in node.keywords:
                self.visit(keyword.value)
            keyword_names = [kw.arg for kw in node.keywords]
            bytecode.append(["CALL_FUNCTION", len(node.args), len(node.keywords), keyword_names])

        def visit_Name(self, node):
            bytecode.append(["LOAD_NAME", node.id])

        def visit_Constant(self, node):
            bytecode.append(["LOAD_VALUE", node.value])

        def visit_Str(self, node):
            bytecode.append(["LOAD_VALUE", node.s])

        def visit_Num(self, node):
            bytecode.append(["LOAD_VALUE", node.n])

        def visit_Attribute(self, node):
            self.visit(node.value)
            bytecode.append(["LOAD_ATTR", node.attr])

        def visit_List(self, node):
            for elt in node.elts:
                self.visit(elt)
            bytecode.append(["BUILD_LIST", len(node.elts)])

        def visit_Tuple(self, node):
            for elt in node.elts:
                self.visit(elt)
            bytecode.append(["BUILD_TUPLE", len(node.elts)])

        def visit_Dict(self, node):
            for key, value in zip(node.keys, node.values):
                self.visit(key)
                self.visit(value)
            bytecode.append(["BUILD_DICT", len(node.keys)])

        def visit_If(self, node):
            self.visit(node.test)
            else_label = len(bytecode) + 1000
            end_label = len(bytecode) + 2000
            bytecode.append(["POP_JUMP_IF_FALSE", else_label])
            for stmt in node.body:
                self.visit(stmt)
            if node.orelse:
                bytecode.append(["JUMP_FORWARD", end_label])
                bytecode.append(["LABEL", else_label])
                for stmt in node.orelse:
                    self.visit(stmt)
                bytecode.append(["LABEL", end_label])
            else:
                bytecode.append(["LABEL", else_label])

        def visit_While(self, node):
            loop_start = len(bytecode)
            bytecode.append(["LABEL", loop_start])
            self.visit(node.test)
            loop_end = len(bytecode) + 1000
            bytecode.append(["POP_JUMP_IF_FALSE", loop_end])
            for stmt in node.body:
                self.visit(stmt)
            bytecode.append(["JUMP_ABSOLUTE", loop_start])
            bytecode.append(["LABEL", loop_end])

        def visit_For(self, node):
            self.visit(node.iter)
            bytecode.append(["GET_ITER"])
            loop_start = len(bytecode)
            bytecode.append(["LABEL", loop_start])
            loop_end = len(bytecode) + 1000
            bytecode.append(["FOR_ITER", loop_end])
            if isinstance(node.target, ast.Name):
                bytecode.append(["STORE_NAME", node.target.id])
            for stmt in node.body:
                self.visit(stmt)
            bytecode.append(["JUMP_ABSOLUTE", loop_start])
            bytecode.append(["LABEL", loop_end])

        def visit_Return(self, node):
            if node.value:
                self.visit(node.value)
            else:
                bytecode.append(["LOAD_VALUE", None])
            bytecode.append(["RETURN_VALUE"])

        def visit_FunctionDef(self, node):
            func_code = []
            func_compiler = VMCompiler()
            func_compiler.bytecode = func_code
            for stmt in node.body:
                func_compiler.visit(stmt)
            bytecode.append(["MAKE_FUNCTION", node.name, func_code, [arg.arg for arg in node.args.args]])

        def visit_Compare(self, node):
            self.visit(node.left)
            for i, (op, comparator) in enumerate(zip(node.ops, node.comparators)):
                self.visit(comparator)
                if isinstance(op, ast.Eq):
                    bytecode.append(["COMPARE_OP", "=="])
                elif isinstance(op, ast.NotEq):
                    bytecode.append(["COMPARE_OP", "!="])
                elif isinstance(op, ast.Lt):
                    bytecode.append(["COMPARE_OP", "<"])
                elif isinstance(op, ast.LtE):
                    bytecode.append(["COMPARE_OP", "<="])
                elif isinstance(op, ast.Gt):
                    bytecode.append(["COMPARE_OP", ">"])
                elif isinstance(op, ast.GtE):
                    bytecode.append(["COMPARE_OP", ">="])
                elif isinstance(op, ast.Is):
                    bytecode.append(["COMPARE_OP", "is"])
                elif isinstance(op, ast.IsNot):
                    bytecode.append(["COMPARE_OP", "is not"])
                elif isinstance(op, ast.In):
                    bytecode.append(["COMPARE_OP", "in"])
                elif isinstance(op, ast.NotIn):
                    bytecode.append(["COMPARE_OP", "not in"])

        def visit_BoolOp(self, node):
            if isinstance(node.op, ast.And):
                for i, value in enumerate(node.values):
                    self.visit(value)
                    if i < len(node.values) - 1:
                        end_label = len(bytecode) + 1000 + i
                        bytecode.append(["JUMP_IF_FALSE_OR_POP", end_label])
                for i in range(len(node.values) - 1):
                    bytecode.append(["LABEL", len(bytecode) + 1000 + i])
            elif isinstance(node.op, ast.Or):
                for i, value in enumerate(node.values):
                    self.visit(value)
                    if i < len(node.values) - 1:
                        end_label = len(bytecode) + 1000 + i
                        bytecode.append(["JUMP_IF_TRUE_OR_POP", end_label])
                for i in range(len(node.values) - 1):
                    bytecode.append(["LABEL", len(bytecode) + 1000 + i])

        def visit_UnaryOp(self, node):
            self.visit(node.operand)
            if isinstance(node.op, ast.UAdd):
                bytecode.append(["UNARY_POSITIVE"])
            elif isinstance(node.op, ast.USub):
                bytecode.append(["UNARY_NEGATIVE"])
            elif isinstance(node.op, ast.Not):
                bytecode.append(["UNARY_NOT"])
            elif isinstance(node.op, ast.Invert):
                bytecode.append(["UNARY_INVERT"])

        def visit_BinOp(self, node):
            self.visit(node.left)
            self.visit(node.right)
            op = type(node.op)
            if op == ast.Add:
                bytecode.append(["BINARY_ADD"])
            elif op == ast.Sub:
                bytecode.append(["BINARY_SUB"])
            elif op == ast.Mult:
                bytecode.append(["BINARY_MUL"])
            elif op == ast.Div:
                bytecode.append(["BINARY_DIV"])
            elif op == ast.FloorDiv:
                bytecode.append(["BINARY_FLOORDIV"])
            elif op == ast.Mod:
                bytecode.append(["BINARY_MOD"])
            elif op == ast.Pow:
                bytecode.append(["BINARY_POW"])
            elif op == ast.LShift:
                bytecode.append(["BINARY_LSHIFT"])
            elif op == ast.RShift:
                bytecode.append(["BINARY_RSHIFT"])
            elif op == ast.BitOr:
                bytecode.append(["BINARY_OR"])
            elif op == ast.BitXor:
                bytecode.append(["BINARY_XOR"])
            elif op == ast.BitAnd:
                bytecode.append(["BINARY_AND"])

        def visit_Subscript(self, node):
            self.visit(node.value)
            self.visit(node.slice)
            bytecode.append(["BINARY_SUBSCR"])

        def visit_Index(self, node):
            self.visit(node.value)

        def visit_Slice(self, node):
            if node.lower:
                self.visit(node.lower)
            else:
                bytecode.append(["LOAD_VALUE", None])
            if node.upper:
                self.visit(node.upper)
            else:
                bytecode.append(["LOAD_VALUE", None])
            if node.step:
                self.visit(node.step)
            else:
                bytecode.append(["LOAD_VALUE", None])
            bytecode.append(["BUILD_SLICE", 3])

        def generic_visit(self, node):
            super().generic_visit(node)

    VMCompiler().visit(tree)
    return bytecode

def encryptBytecode(bytecode):
    raw = marshal.dumps(bytecode)
    compressed = zlib.compress(raw)
    encoded = base64.b64encode(compressed).decode()
    return encoded

def generateVMRunner(encryptedBytecode):
    return f'''
import base64, marshal, zlib, sys, ctypes
import builtins
import importlib
import operator

def antiDebug():
    if sys.gettrace() or ctypes.windll.kernel32.IsDebuggerPresent():
        exit()

def runVm(bytecode, context):
    stack = []
    pc = 0
    labels = {{}}
    
    for i, inst in enumerate(bytecode):
        if inst[0] == "LABEL":
            labels[inst[1]] = i
    
    while pc < len(bytecode):
        inst = bytecode[pc]
        op = inst[0]
        
        if op == "LOAD_VALUE":
            stack.append(inst[1])
        elif op == "LOAD_NAME":
            name = inst[1]
            if name in context:
                stack.append(context[name])
            elif hasattr(builtins, name):
                stack.append(getattr(builtins, name))
            else:
                raise NameError(f"name '{{name}}' is not defined")
        elif op == "LOAD_ATTR":
            obj = stack.pop()
            attr_name = inst[1]
            try:
                attr_value = getattr(obj, attr_name)
                stack.append(attr_value)
            except Exception as e:
                stack.append(None)
        elif op == "STORE_NAME":
            if stack:
                context[inst[1]] = stack.pop()
        elif op == "POP_TOP":
            if stack:
                stack.pop()
        elif op == "IMPORT":
            module_name = inst[1]
            store_name = inst[2]
            try:
                module = importlib.import_module(module_name)
                context[store_name] = module
            except ImportError:
                context[store_name] = None
        elif op == "IMPORT_FROM":
            module_name = inst[1]
            attr_name = inst[2]
            store_name = inst[3]
            try:
                if module_name:
                    module = importlib.import_module(module_name)
                    attr_value = getattr(module, attr_name)
                    context[store_name] = attr_value
                else:
                    raise ImportError("Relative imports not supported")
            except (ImportError, AttributeError):
                context[store_name] = None
        elif op == "UNPACK_SEQUENCE":
            seq = stack.pop()
            count = inst[1]
            items = list(seq)
            if len(items) != count:
                raise ValueError(f"too many values to unpack (expected {{count}})")
            for item in reversed(items):
                stack.append(item)
        elif op == "BUILD_LIST":
            count = inst[1]
            items = []
            for _ in range(count):
                items.insert(0, stack.pop())
            stack.append(items)
        elif op == "BUILD_TUPLE":
            count = inst[1]
            items = []
            for _ in range(count):
                items.insert(0, stack.pop())
            stack.append(tuple(items))
        elif op == "BUILD_DICT":
            count = inst[1]
            d = {{}}
            for _ in range(count):
                value = stack.pop()
                key = stack.pop()
                d[key] = value
            stack.append(d)
        elif op == "BINARY_ADD":
            b = stack.pop()
            a = stack.pop()
            stack.append(a + b)
        elif op == "BINARY_SUB":
            b = stack.pop()
            a = stack.pop()
            stack.append(a - b)
        elif op == "BINARY_MUL":
            b = stack.pop()
            a = stack.pop()
            stack.append(a * b)
        elif op == "BINARY_DIV":
            b = stack.pop()
            a = stack.pop()
            stack.append(a / b)
        elif op == "BINARY_FLOORDIV":
            b = stack.pop()
            a = stack.pop()
            stack.append(a // b)
        elif op == "BINARY_MOD":
            b = stack.pop()
            a = stack.pop()
            stack.append(a % b)
        elif op == "BINARY_POW":
            b = stack.pop()
            a = stack.pop()
            stack.append(a ** b)
        elif op == "BINARY_LSHIFT":
            b = stack.pop()
            a = stack.pop()
            stack.append(a << b)
        elif op == "BINARY_RSHIFT":
            b = stack.pop()
            a = stack.pop()
            stack.append(a >> b)
        elif op == "BINARY_OR":
            b = stack.pop()
            a = stack.pop()
            stack.append(a | b)
        elif op == "BINARY_XOR":
            b = stack.pop()
            a = stack.pop()
            stack.append(a ^ b)
        elif op == "BINARY_AND":
            b = stack.pop()
            a = stack.pop()
            stack.append(a & b)
        elif op == "BINARY_SUBSCR":
            index = stack.pop()
            obj = stack.pop()
            stack.append(obj[index])
        elif op == "COMPARE_OP":
            comp_op = inst[1]
            b = stack.pop()
            a = stack.pop()
            if comp_op == "==":
                result = a == b
            elif comp_op == "!=":
                result = a != b
            elif comp_op == "<":
                result = a < b
            elif comp_op == "<=":
                result = a <= b
            elif comp_op == ">":
                result = a > b
            elif comp_op == ">=":
                result = a >= b
            elif comp_op == "is":
                result = a is b
            elif comp_op == "is not":
                result = a is not b
            elif comp_op == "in":
                result = a in b
            elif comp_op == "not in":
                result = a not in b
            stack.append(result)
        elif op == "UNARY_POSITIVE":
            a = stack.pop()
            stack.append(+a)
        elif op == "UNARY_NEGATIVE":
            a = stack.pop()
            stack.append(-a)
        elif op == "UNARY_NOT":
            a = stack.pop()
            stack.append(not a)
        elif op == "UNARY_INVERT":
            a = stack.pop()
            stack.append(~a)
        elif op == "POP_JUMP_IF_FALSE":
            target = inst[1]
            if not stack.pop():
                pc = labels.get(target, pc)
                continue
        elif op == "POP_JUMP_IF_TRUE":
            target = inst[1]
            if stack.pop():
                pc = labels.get(target, pc)
                continue
        elif op == "JUMP_FORWARD":
            target = inst[1]
            pc = labels.get(target, pc)
            continue
        elif op == "JUMP_ABSOLUTE":
            target = inst[1]
            pc = labels.get(target, pc)
            continue
        elif op == "JUMP_IF_FALSE_OR_POP":
            target = inst[1]
            if not stack[-1]:
                pc = labels.get(target, pc)
                continue
            else:
                stack.pop()
        elif op == "JUMP_IF_TRUE_OR_POP":
            target = inst[1]
            if stack[-1]:
                pc = labels.get(target, pc)
                continue
            else:
                stack.pop()
        elif op == "GET_ITER":
            obj = stack.pop()
            stack.append(iter(obj))
        elif op == "FOR_ITER":
            target = inst[1]
            iterator = stack[-1]
            try:
                value = next(iterator)
                stack.append(value)
            except StopIteration:
                stack.pop()
                pc = labels.get(target, pc)
                continue
        elif op == "RETURN_VALUE":
            return stack.pop() if stack else None
        elif op == "MAKE_FUNCTION":
            func_name = inst[1]
            func_code = inst[2]
            func_args = inst[3]
            def func(*args, **kwargs):
                func_context = context.copy()
                for i, arg in enumerate(func_args):
                    if i < len(args):
                        func_context[arg] = args[i]
                for key, value in kwargs.items():
                    func_context[key] = value
                return runVm(func_code, func_context)
            context[func_name] = func
        elif op == "BUILD_SLICE":
            count = inst[1]
            if count == 3:
                step = stack.pop()
                stop = stack.pop()
                start = stack.pop()
                stack.append(slice(start, stop, step))
            elif count == 2:
                stop = stack.pop()
                start = stack.pop()
                stack.append(slice(start, stop))
        elif op == "CALL_FUNCTION":
            argc = inst[1]
            kwargc = inst[2]
            keyword_names = inst[3] if len(inst) > 3 else []
            
            kwargs = {{}}
            for i in range(kwargc):
                value = stack.pop()
                key = keyword_names[kwargc - 1 - i]
                kwargs[key] = value
            
            args = []
            for i in range(argc):
                args.append(stack.pop())
            args.reverse()
            
            func = stack.pop()
            
            if not callable(func):
                raise TypeError(f"'{{type(func).__name__}}' object is not callable")
            
            if kwargs:
                result = func(*args, **kwargs)
            else:
                result = func(*args)
            stack.append(result)
        elif op == "LABEL":
            pass
        
        pc += 1

antiDebug()
data = base64.b64decode("{encryptedBytecode}")
bytecode = marshal.loads(zlib.decompress(data))
ctx = {{}}
runVm(bytecode, ctx)
'''.strip()

def obfuscateFile(inputPath, outputPath):
    with open(inputPath, "r", encoding="utf-8") as f:
        source = f.read()
    
    filtered_source = []
    lines = source.split('\n')
    skip_main = False
    in_function = False
    in_class = False
    
    for line in lines:
        stripped = line.strip()
        
        if stripped.startswith('def ') or stripped.startswith('class '):
            in_function = True
        elif in_function and line and not line[0].isspace():
            in_function = False
            
        if stripped.startswith('if __name__ == "__main__":'):
            skip_main = True
            continue
        elif skip_main and (line.startswith('    ') or line.startswith('\t') or stripped == ''):
            continue
        elif skip_main and not (line.startswith('    ') or line.startswith('\t')):
            skip_main = False
        
        if not skip_main:
            if not in_function and not in_class:
                if any(flask_attr in line for flask_attr in ['request.', 'session.', 'g.', 'current_app.']):
                    line = line.replace(line.strip(), f"# {line.strip()}")
            filtered_source.append(line)
    
    source = '\n'.join(filtered_source)

    antiDebug()
    bytecode = compileSourceToBytecode(source)
    encrypted = encryptBytecode(bytecode)
    obfuscatedCode = generateVMRunner(encrypted)

    with open(outputPath, "w", encoding="utf-8") as f:
        f.write(obfuscatedCode)
    print(f"[✔] Obfuscated file saved to: {outputPath}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python vm_obfuscator.py <input.py> <output.py>")
        sys.exit(1)

    inputFile = sys.argv[1]
    outputFile = sys.argv[2]
    obfuscateFile(inputFile, outputFile)