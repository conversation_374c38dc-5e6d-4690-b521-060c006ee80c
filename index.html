<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>King <PERSON> Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'JetBrains Mono', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
            transition: all 0.3s ease;
        }
        
        /* Light theme styles */
        body.light-theme {
            background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
            color: #333333;
        }
        
        body.light-theme .sidebar {
            background: linear-gradient(180deg, #ffffff 0%, #f0f0f0 100%);
            border-right: 1px solid #ddd;
        }
        
        body.light-theme .sidebarHeader {
            border-bottom: 1px solid #ddd;
        }
        
        body.light-theme .logo {
            color: #333;
        }
        
        body.light-theme .logoSub {
            color: #666;
        }
        
        body.light-theme .navItem {
            color: #555;
        }
        
        body.light-theme .navItem:hover,
        body.light-theme .navItem.active {
            background: rgba(0, 0, 0, 0.1);
            color: #333;
        }
        
        body.light-theme .userCard,
        body.light-theme .bannedCard {
            background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
            border: 1px solid #ddd;
            color: #333;
        }
        
        body.light-theme .sectionTitle {
            color: #333;
        }
        
        body.light-theme .exchangeHistoryItem,
        body.light-theme .banHistoryItem {
            background: rgba(0, 0, 0, 0.05);
            border: 1px solid #ddd;
            color: #333;
        }
        
        body.light-theme .btn {
            color: #333;
        }
        
        body.light-theme .btnSecondary {
            background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
            border: 1px solid #ddd;
        }
        
        body.light-theme .btnSuccess {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        body.light-theme .btnDanger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        
        body.light-theme .themeToggle {
            background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
            border: 1px solid #ddd;
            color: #333;
        }
        
        /* Custom Popup Styles */
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }
        
        .popup {
            background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
            border: 1px solid #444;
            border-radius: 15px;
            padding: 30px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            text-align: center;
            position: relative;
            animation: slideIn 0.3s ease;
            box-shadow: 0 20px 40px rgba(0,0,0,0.5);
        }
        
        .popup-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .popup-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 15px;
            color: #fff;
        }
        
        .popup-message {
            font-size: 16px;
            color: #ccc;
            margin-bottom: 25px;
            line-height: 1.5;
            text-align: left;
        }
        
        .popup-message h3, .popup-message h4 {
            color: #fff;
            margin-bottom: 10px;
        }
        
        .popup-message .btn {
            margin: 2px;
        }
        
        .popup-message div[style*="background: #333"] {
            margin-bottom: 15px;
        }
        
        .popup-message div[style*="background: rgba(255,193,7,0.1)"] {
            margin-bottom: 8px;
        }
        
        .popup-message div[style*="background: rgba(255,68,68,0.1)"] {
            margin-bottom: 10px;
        }
        
        .popup-message div[style*="background: #444"] {
            margin-bottom: 8px;
        }
        
        .popup-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .popup-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .popup-btn.confirm {
            background: linear-gradient(135deg, #ff4444, #cc3333);
            color: white;
        }
        
        .popup-btn.cancel {
            background: linear-gradient(135deg, #666, #444);
            color: white;
        }
        
        .popup-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .popup-btn.confirm:hover {
            background: linear-gradient(135deg, #ff6666, #ff4444);
        }
        
        .popup-btn.cancel:hover {
            background: linear-gradient(135deg, #888, #666);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideIn {
            from { 
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to { 
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
            border-right: 1px solid #333;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebarHeader {
            padding: 30px 25px;
            border-bottom: 1px solid #333;
            text-align: center;
        }
        
        .logo {
            font-size: 20px;
            font-weight: 700;
            color: #fff;
            margin-bottom: 8px;
        }
        
        .logoSub {
            font-size: 12px;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .navMenu {
            padding: 20px 0;
        }
        
        .navItem {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: #ccc;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            cursor: pointer;
        }
        
        .navItem:hover {
            background: rgba(255,255,255,0.05);
            color: #fff;
            border-left-color: #fff;
        }
        
        .navItem.active {
            background: rgba(255,255,255,0.1);
            color: #fff;
            border-left-color: #fff;
        }
        
        .navIcon {
            width: 20px;
            margin-right: 15px;
            text-align: center;
        }
        
        .mainContent {
            margin-left: 280px;
            padding: 30px;
            min-height: 100vh;
        }
        
        .topBar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding: 20px 0;
            border-bottom: 1px solid #333;
        }
        
        .pageTitle {
            font-size: 28px;
            font-weight: 700;
            color: #fff;
        }
        
        .topControls {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .searchBox {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 10px 15px;
            color: #fff;
            font-family: inherit;
            font-size: 14px;
            width: 250px;
            transition: all 0.3s ease;
        }
        
        .searchBox:focus {
            outline: none;
            border-color: #666;
            background: #333;
        }
        
        .themeToggle {
            background: #2a2a2a;
            border: 1px solid #444;
            color: #fff;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .themeToggle:hover {
            background: #fff;
            color: #000;
        }
        
        .statsGrid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .statCard {
            background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
            border: 1px solid #444;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .statCard::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #fff, #ccc);
        }
        
        .statCard:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .statValue {
            font-size: 36px;
            font-weight: 700;
            color: #fff;
            margin-bottom: 8px;
        }
        
        .statLabel {
            font-size: 12px;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .statIcon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            color: #444;
        }
        
        .contentSection {
            display: none;
        }
        
        .contentSection.active {
            display: block;
        }
        
        .userGrid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 25px;
        }
        
        .userCard {
            background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
            border: 1px solid #444;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .userCard:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }
        
        .userCard.suspicious {
            background: linear-gradient(135deg, rgba(255,68,68,0.2) 0%, rgba(255,68,68,0.1) 100%);
            border-color: #ff4444;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 20px rgba(255,68,68,0.3);
        }
        
        .userCard.suspicious::before {
            content: '🚨 CẢNH BÁO BYPASS';
            position: absolute;
            top: 15px;
            right: 15px;
            background: #ff4444;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 68, 68, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0); }
        }
        
        .userHeader {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #333;
        }
        
        .userAvatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #444, #666);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 20px;
            color: #fff;
            border: 3px solid #555;
            overflow: hidden;
        }
        
        .userAvatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .userInfo h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #fff;
        }
        
        .userInfo p {
            font-size: 12px;
            color: #888;
            margin-bottom: 3px;
        }
        
        .userStats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .userStat {
            background: #333;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #444;
        }
        
        .userStat .value {
            font-size: 20px;
            font-weight: 700;
            color: #fff;
            margin-bottom: 5px;
        }
        
        .userStat .label {
            font-size: 10px;
            color: #888;
            text-transform: uppercase;
        }
        
        .exchangeStats {
            background: rgba(255,193,7,0.1);
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .exchangeStats h4 {
            color: #ffc107;
            font-size: 14px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .exchangeStatsGrid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .exchangeStat {
            text-align: center;
            padding: 8px;
            background: rgba(255,193,7,0.05);
            border-radius: 6px;
        }
        
        .exchangeStat .value {
            font-size: 16px;
            font-weight: 700;
            color: #ffc107;
        }
        
        .exchangeStat .label {
            font-size: 10px;
            color: #ccc;
        }
        
        .activitySection {
            margin-top: 20px;
        }
        
        .sectionTitle {
            font-size: 14px;
            font-weight: 600;
            color: #fff;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .activityList {
            max-height: 250px;
            overflow-y: auto;
            padding-right: 10px;
        }
        
        .activityItem {
            background: #333;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .activityItem:hover {
            background: #3a3a3a;
        }
        
        .activityItem.suspicious {
            border-left: 4px solid #ff4444;
            background: rgba(255,68,68,0.1);
        }
        
        .activityHeader {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .activitySite {
            background: #444;
            color: #fff;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .activityTime {
            font-size: 11px;
            color: #888;
        }
        
        .activityDetails {
            font-size: 11px;
            color: #ccc;
            line-height: 1.4;
        }
        
        .timeDiff {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 600;
            font-size: 10px;
        }
        
        .timeDiff.normal {
            background: #28a745;
            color: white;
        }
        
        .timeDiff.suspicious {
            background: #ff4444;
            color: white;
        }
        
        .userActions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #333;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-family: inherit;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }
        
        .btnDanger {
            background: #ff4444;
            color: white;
        }
        
        .btnSuccess {
            background: #28a745;
            color: white;
        }
        
        .btnSecondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        .exchangeGrid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 25px;
        }
        
        .exchangeCard {
            background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
            border: 1px solid #444;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
        }
        
        .exchangeCard:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }
        
        .pointsDisplay {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #333;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #444;
        }
        
        .pointsLabel {
            font-size: 12px;
            color: #888;
            text-transform: uppercase;
        }
        
        .pointsValue {
            font-size: 18px;
            font-weight: 700;
            color: #fff;
        }
        
        .exchangeHistory {
            margin-top: 20px;
        }
        
        .exchangeHistoryItem {
            background: rgba(255,193,7,0.05);
            border: 1px solid rgba(255,193,7,0.2);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            font-size: 12px;
        }
        
        .exchangeHistoryHeader {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .exchangeHistoryTime {
            color: #ffc107;
            font-weight: 600;
        }
        
        .exchangeHistoryDetails {
            color: #ccc;
        }
        
        .bannedGrid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 25px;
        }
        
        .bannedCard {
            background: linear-gradient(135deg, rgba(255,68,68,0.15) 0%, rgba(255,68,68,0.05) 100%);
            border: 1px solid #ff4444;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
        }
        
        .bannedCard:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255,68,68,0.2);
        }
        
        .banInfo {
            background: rgba(255,68,68,0.1);
            border: 1px solid #ff4444;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .banDetail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 12px;
        }
        
        .banLabel {
            color: #ff9999;
            font-weight: 600;
        }
        
        .banValue {
            color: #fff;
        }
        
        .banHistory {
            margin-top: 15px;
        }
        
        .banHistoryItem {
            background: rgba(255,68,68,0.05);
            border: 1px solid rgba(255,68,68,0.2);
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 8px;
            font-size: 11px;
        }
        
        .banHistoryHeader {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .banHistoryTime {
            color: #ff9999;
            font-weight: 600;
        }
        
        .banHistoryDetails {
            color: #ccc;
        }
        
        .scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .scrollbar::-webkit-scrollbar-track {
            background: #333;
            border-radius: 3px;
        }
        
        .scrollbar::-webkit-scrollbar-thumb {
            background: #666;
            border-radius: 3px;
        }
        
        .scrollbar::-webkit-scrollbar-thumb:hover {
            background: #888;
        }
        
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .mainContent {
                margin-left: 0;
            }
            
            .userGrid, .exchangeGrid, .bannedGrid {
                grid-template-columns: 1fr;
            }
            
            .statsGrid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .topBar {
                flex-direction: column;
                gap: 20px;
            }
            
            .topControls {
                width: 100%;
                justify-content: center;
            }
            
            .searchBox {
                width: 200px;
            }
            
            .statsGrid {
                grid-template-columns: 1fr;
            }
        }
        .mobileHeader {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
            z-index: 2000;
            padding: 18px 0 8px 0;
            text-align: center;
            border-bottom: 1px solid #333;
        }
        .mobileHeader .logo {
            font-size: 20px;
            font-weight: 700;
            color: #fff;
            margin-bottom: 2px;
        }
        .mobileHeader .logoSub {
            font-size: 12px;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .mobileTabBar {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100vw;
            background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
            border-top: 1px solid #333;
            z-index: 2000;
            display: flex;
            justify-content: space-around;
            align-items: center;
            height: 56px;
        }
        .mobileTab {
            flex: 1;
            text-align: center;
            color: #ccc;
            font-size: 13px;
            padding: 6px 0 2px 0;
            cursor: pointer;
            transition: color 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .mobileTab.active {
            color: #fff;
            font-weight: 700;
        }
        .mobileTab i {
            font-size: 18px;
            margin-bottom: 2px;
        }
        @media (max-width: 1024px) {
            .sidebar {
                display: none;
            }
            .mainContent {
                margin-left: 0;
                padding-top: 60px;
                padding-bottom: 70px;
            }
            .mobileHeader {
                display: block;
            }
            .mobileTabBar {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <!-- Custom Popup -->
    <div class="popup-overlay" id="popupOverlay">
        <div class="popup" id="popup">
            <div class="popup-icon" id="popupIcon"></div>
            <div class="popup-title" id="popupTitle">Xác nhận</div>
            <div class="popup-message" id="popupMessage">Bạn có chắc chắn muốn thực hiện hành động này?</div>
            <div class="popup-buttons" id="popupButtons">
                <button class="popup-btn confirm" id="popupConfirm">Xác nhận</button>
                <button class="popup-btn cancel" id="popupCancel">Hủy</button>
            </div>
        </div>
    </div>

    <div class="sidebar">
        <div class="sidebarHeader" id="sidebarHeader">
            <div class="logo">King Bot</div>
            <div class="logoSub">Bảng điều khiển</div>
        </div>
        <nav class="navMenu">
            <div class="navItem active" onclick="showSection('users', event)">
                <i class="fas fa-users navIcon"></i>
                <span>Hoạt động người dùng</span>
            </div>
            <div class="navItem" onclick="showSection('exchange', event)">
                <i class="fas fa-coins navIcon"></i>
                <span>Đổi điểm</span>
            </div>
            <div class="navItem" onclick="showSection('banned', event)">
                <i class="fas fa-ban navIcon"></i>
                <span>Người dùng bị cấm</span>
            </div>
        </nav>
    </div>

    <!-- Mobile Top Header -->
    <div class="mobileHeader">
        <div class="logo">King Bot</div>
        <div class="logoSub">Bảng điều khiển</div>
    </div>

    <div class="mainContent">
        <div class="topBar">
            <h1 class="pageTitle">D A S H  B O A R D</h1>
            <div class="topControls">
                <input type="text" class="searchBox" placeholder="Tìm kiếm người dùng..." id="searchBox">
                <button class="themeToggle" onclick="toggleTheme()">
                    <i class="fas fa-moon"></i> Giao diện
                </button>
            </div>
        </div>
        
        <div class="statsGrid">
            <div class="statCard">
                <i class="fas fa-users statIcon"></i>
                <div class="statValue">{{ totalUsers }}</div>
                <div class="statLabel">Tổng người dùng</div>
            </div>
            <div class="statCard">
                <i class="fas fa-exclamation-triangle statIcon"></i>
                <div class="statValue">{{ suspiciousCount }}</div>
                <div class="statLabel">Bypass được phát hiện</div>
            </div>
            <div class="statCard">
                <i class="fas fa-ban statIcon"></i>
                <div class="statValue">{{ bannedCount }}</div>
                <div class="statLabel">Người dùng bị cấm</div>
            </div>
            <div class="statCard">
                <i class="fas fa-coins statIcon"></i>
                <div class="statValue">{{ totalPoints }}</div>
                <div class="statLabel">Tổng điểm</div>
            </div>
            <div class="statCard">
                <i class="fas fa-tasks statIcon"></i>
                <div class="statValue">{{ totalTasks }}</div>
                <div class="statLabel">Nhiệm vụ hoàn thành</div>
            </div>
            <div class="statCard">
                <i class="fas fa-exchange-alt statIcon"></i>
                <div class="statValue">{{ totalExchanges }}</div>
                <div class="statLabel">Lần đổi điểm</div>
            </div>
        </div>
        
        <div id="users" class="contentSection active">
            <div class="userGrid">
                {% for user in users %}
                <div class="userCard {% if user.suspicious %}suspicious{% endif %}" data-user="{{ user.username|lower }}">
                    <div class="userHeader">
                        <div class="userAvatar">
                            {% if user.avatar %}
                                <img src="{{ user.avatar }}" alt="Avatar">
                            {% else %}
                                {{ user.displayName[0] if user.displayName else 'U' }}
                            {% endif %}
                        </div>
                        <div class="userInfo">
                            <h3>{{ user.displayName }}</h3>
                            <p><i class="fas fa-at"></i> {{ user.username }}</p>
                            <p><i class="fas fa-id-card"></i> {{ user.id }}</p>
                        </div>
                    </div>
                    
                    <div class="userStats">
                        <div class="userStat">
                            <div class="value">{{ user.yeumoneyPoints }}</div>
                            <div class="label">Yeumoney</div>
                        </div>
                        <div class="userStat">
                            <div class="value">{{ user.site2sPoints }}</div>
                            <div class="label">Site2s</div>
                        </div>
                        <div class="userStat">
                            <div class="value">{{ user.totalPoints }}</div>
                            <div class="label">Tổng</div>
                        </div>
                    </div>
                    
                    <div class="activitySection">
                        <div class="sectionTitle">
                            <i class="fas fa-history"></i>
                            Hoạt động gần đây
                        </div>
                        <div class="activityList scrollbar">
                            {% for log in user.logs %}
                            <div class="activityItem {% if log.bypassDetected %}suspicious{% endif %}">
                                <div class="activityHeader">
                                    <span class="activitySite">{{ log.site }}</span>
                                    <span class="activityTime">{{ log.timestamp }}</span>
                                </div>
                                <div class="activityDetails">
                                    <i class="fas fa-globe"></i> IP: {{ log.ip }}<br>
                                    <i class="fas fa-browser"></i> Trình duyệt: {{ log.browser }}<br>
                                    <i class="fas fa-clock"></i> Chênh lệch thời gian: 
                                    <span class="timeDiff {% if log.bypassDetected %}suspicious{% else %}normal{% endif %}">
                                        {{ log.timeDiff }}s
                                    </span>
                                    {% if log.bypassDetected %}
                                    <br><strong><i class="fas fa-exclamation-triangle"></i> PHÁT HIỆN BYPASS</strong>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <div class="userActions">
                        {% if user.banned %}
                            <button class="btn btnSuccess" onclick="showUnbanPopup('{{ user.id }}')">
                                <i class="fas fa-unlock"></i> Bỏ cấm
                            </button>
                        {% else %}
                            <button class="btn btnDanger" onclick="showBanPopup('{{ user.id }}')">
                                <i class="fas fa-ban"></i> Cấm
                            </button>
                        {% endif %}
                        <button class="btn btnSecondary" onclick="showUserDetailsPopup('{{ user.id }}')">
                            <i class="fas fa-eye"></i> Xem chi tiết
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div id="exchange" class="contentSection">
            <div class="exchangeGrid">
                {% for user in exchangeUsers %}
                <div class="exchangeCard">
                    <div class="userHeader">
                        <div class="userAvatar">
                            {% if user.avatar %}
                                <img src="{{ user.avatar }}" alt="Avatar">
                            {% else %}
                                {{ user.displayName[0] if user.displayName else 'U' }}
                            {% endif %}
                        </div>
                        <div class="userInfo">
                            <h3>{{ user.displayName }}</h3>
                            <p><i class="fas fa-at"></i> {{ user.username }}</p>
                        </div>
                    </div>
                    
                    <div class="pointsDisplay">
                        <div>
                            <div class="pointsLabel">Điểm Yeumoney</div>
                            <div class="pointsValue">{{ user.yeumoneyPoints }}</div>
                        </div>
                        <div>
                            <div class="pointsLabel">Điểm Site2s</div>
                            <div class="pointsValue">{{ user.site2sPoints }}</div>
                        </div>
                    </div>
                    
                    <div class="pointsDisplay">
                        <div>
                            <div class="pointsLabel">Tổng điểm</div>
                            <div class="pointsValue">{{ user.totalPoints }}</div>
                        </div>
                        <div>
                            <div class="pointsLabel">Tài khoản có thể đổi</div>
                            <div class="pointsValue">{{ (user.totalPoints // 4) }}</div>
                        </div>
                    </div>
                    
                    {% if user.totalExchanges > 0 %}
                    <div class="exchangeHistory">
                        <div class="sectionTitle">
                            <i class="fas fa-history"></i>
                            Lịch sử đổi điểm ({{ user.totalExchanges }} lần)
                        </div>
                        {% if user.exchangeHistory %}
                        <div class="exchangeHistoryItem">
                            <div class="exchangeHistoryHeader">
                                <span class="exchangeHistoryTime">{{ user.exchangeHistory[-1].timestamp }}</span>
                                <span>{{ user.exchangeHistory[-1].exchangeType }}</span>
                            </div>
                            <div class="exchangeHistoryDetails">
                                Đã đổi {{ user.exchangeHistory[-1].pointsExchanged }} điểm → Nhận {{ user.exchangeHistory[-1].accountsReceived }} tài khoản
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="exchangeStats">
                            <h4><i class="fas fa-chart-bar"></i> Thống kê điểm</h4>
                            <div class="exchangeStatsGrid">
                                <div class="exchangeStat">
                                    <div class="value">{{ user.totalExchanges }}</div>
                                    <div class="label">Lần đổi</div>
                                </div>
                                <div class="exchangeStat">
                                    <div class="value">{{ user.totalAccountsReceived }}</div>
                                    <div class="label">Tài khoản nhận</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="userActions">
                        <button class="btn btnSecondary" onclick="showUserDetailsPopup('{{ user.id }}')">
                            <i class="fas fa-eye"></i> Chi tiết đầy đủ
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div id="banned" class="contentSection">
            <div class="bannedGrid">
                {% for user in bannedUsers %}
                <div class="bannedCard">
                    <div class="userHeader">
                        <div class="userAvatar">
                            {% if user.avatar %}
                                <img src="{{ user.avatar }}" alt="Avatar">
                            {% else %}
                                {{ user.displayName[0] if user.displayName else 'U' }}
                            {% endif %}
                        </div>
                        <div class="userInfo">
                            <h3>{{ user.displayName }}</h3>
                            <p><i class="fas fa-at"></i> {{ user.username }}</p>
                            <p><i class="fas fa-id-card"></i> {{ user.id }}</p>
                        </div>
                    </div>
                    
                    <div class="banInfo">
                        <div class="banDetail">
                            <span class="banLabel">Số lần cấm:</span>
                            <span class="banValue">{{ user.banCount }}</span>
                        </div>
                        <div class="banDetail">
                            <span class="banLabel">Thời hạn:</span>
                            <span class="banValue">{{ user.duration }}</span>
                        </div>
                        <div class="banDetail">
                            <span class="banLabel">Lý do:</span>
                            <span class="banValue">{{ user.reason }}</span>
                        </div>
                        <div class="banDetail">
                            <span class="banLabel">Trạng thái:</span>
                            <span class="banValue">{{ user.status }}</span>
                        </div>
                    </div>
                    
                    {% if user.banHistory %}
                    <div class="banHistory">
                        <div class="sectionTitle">
                            <i class="fas fa-history"></i>
                            Lịch sử cấm gần đây
                        </div>
                        {% for ban in user.banHistory %}
                        <div class="banHistoryItem">
                            <div class="banHistoryHeader">
                                <span class="banHistoryTime">{{ ban.banTime }}</span>
                                <span>{{ ban.duration }} ngày</span>
                            </div>
                            <div class="banHistoryDetails">
                                Lý do: {{ ban.reason }}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="userActions">
                        <button class="btn btnSuccess" onclick="showUnbanPopup('{{ user.id }}')">
                            <i class="fas fa-unlock"></i> Bỏ cấm người dùng
                        </button>
                        <button class="btn btnSecondary" onclick="showBanHistoryPopup('{{ user.id }}')">
                            <i class="fas fa-history"></i> Xem lịch sử cấm
                        </button>
                        <button class="btn btnDanger" onclick="showDeleteBanHistoryPopup('{{ user.id }}')">
                            <i class="fas fa-trash"></i> Xóa lịch sử cấm
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Mobile Tab Bar -->
    <div class="mobileTabBar">
        <div class="mobileTab active" data-section="users" onclick="showSection('users', event)">
            <i class="fas fa-users"></i>
            <span>Hoạt động</span>
        </div>
        <div class="mobileTab" data-section="exchange" onclick="showSection('exchange', event)">
            <i class="fas fa-coins"></i>
            <span>Đổi điểm</span>
        </div>
        <div class="mobileTab" data-section="banned" onclick="showSection('banned', event)">
            <i class="fas fa-ban"></i>
            <span>Bị cấm</span>
        </div>
    </div>

    <script>
        // Popup functions
        function showPopup(title, message, icon, onConfirm, onCancel, customButtons = null) {
            document.getElementById('popupTitle').textContent = title;
            document.getElementById('popupMessage').innerHTML = message;
            document.getElementById('popupIcon').textContent = icon;
            
            const overlay = document.getElementById('popupOverlay');
            const buttonsContainer = document.getElementById('popupButtons');

            buttonsContainer.innerHTML = '';
            
            if (customButtons) {
                customButtons.forEach(btn => {
                    const button = document.createElement('button');
                    button.className = `popup-btn ${btn.class}`;
                    button.textContent = btn.text;
                    button.onclick = btn.onclick;
                    buttonsContainer.appendChild(button);
                });
            } else {
                const confirmBtn = document.createElement('button');
                confirmBtn.className = 'popup-btn confirm';
                confirmBtn.textContent = 'Xác nhận';
                confirmBtn.onclick = () => {
                    overlay.style.display = 'none';
                    if (onConfirm) onConfirm();
                };
                
                const cancelBtn = document.createElement('button');
                cancelBtn.className = 'popup-btn cancel';
                cancelBtn.textContent = 'Hủy';
                cancelBtn.onclick = () => {
                    overlay.style.display = 'none';
                    if (onCancel) onCancel();
                };
                
                buttonsContainer.appendChild(confirmBtn);
                buttonsContainer.appendChild(cancelBtn);
            }
            
            overlay.style.display = 'flex';
            
            overlay.onclick = (e) => {
                if (e.target === overlay) {
                    overlay.style.display = 'none';
                    if (onCancel) onCancel();
                }
            };
        }
        
        function showSection(sectionName, event) {
            document.querySelectorAll('.navItem').forEach(item => item.classList.remove('active'));
            document.querySelectorAll('.contentSection').forEach(section => section.classList.remove('active'));
            document.querySelectorAll('.mobileTab').forEach(tab => tab.classList.remove('active'));
            if (event && event.target.closest('.navItem')) {
                event.target.closest('.navItem').classList.add('active');
            }
            if (event && event.target.closest('.mobileTab')) {
                event.target.closest('.mobileTab').classList.add('active');
            }
            document.getElementById(sectionName).classList.add('active');
        }
        
        function toggleTheme() {
            document.body.classList.toggle('light-theme');
            const isLightTheme = document.body.classList.contains('light-theme');
            localStorage.setItem('theme', isLightTheme ? 'light' : 'dark');
        }
        
        // Load saved theme on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                document.body.classList.add('light-theme');
            }
        });
        
        function showBanPopup(userId) {
            const banOptions = [
                { text: '1 ngày', class: 'cancel', onclick: () => banUser(userId, 1) },
                { text: '3 ngày', class: 'cancel', onclick: () => banUser(userId, 3) },
                { text: '7 ngày', class: 'cancel', onclick: () => banUser(userId, 7) },
                { text: '30 ngày', class: 'cancel', onclick: () => banUser(userId, 30) },
                { text: 'Vĩnh viễn', class: 'confirm', onclick: () => banUser(userId, -1) },
                { text: 'Hủy', class: 'cancel', onclick: () => document.getElementById('popupOverlay').style.display = 'none' }
            ];
            
            showPopup(
                'Chọn thời hạn cấm',
                'Chọn thời hạn cấm cho người dùng này:',
                '🚫',
                null,
                null,
                banOptions
            );
        }
        
        function showUnbanPopup(userId) {
            showPopup(
                'Xác nhận bỏ cấm',
                'Bạn có chắc chắn muốn bỏ cấm người dùng này không?',
                '🔓',
                () => unbanUser(userId),
                null
            );
        }
        
        function showUserDetailsPopup(userId) {
            fetch(`/user-details?userId=${userId}`)
                .then(response => response.json())
                .then(data => {
                    let detailsHtml = `
                        <div style="text-align: left;">
                            <h3 style="color: #fff; margin-bottom: 15px;">${data.user.displayName}</h3>
                            
                            <div style="background: #333; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <h4 style="color: #ffc107; margin-bottom: 10px;">📊 Thống kê điểm</h4>
                                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                    <div style="text-align: center; padding: 8px; background: #444; border-radius: 6px;">
                                        <div style="font-size: 18px; font-weight: 700; color: #fff;">${data.points.yeumoney}</div>
                                        <div style="font-size: 10px; color: #ccc;">Yeumoney</div>
                                    </div>
                                    <div style="text-align: center; padding: 8px; background: #444; border-radius: 6px;">
                                        <div style="font-size: 18px; font-weight: 700; color: #fff;">${data.points.site2s}</div>
                                        <div style="font-size: 10px; color: #ccc;">Site2s</div>
                                    </div>
                                    <div style="text-align: center; padding: 8px; background: #444; border-radius: 6px;">
                                        <div style="font-size: 18px; font-weight: 700; color: #fff;">${data.points.total}</div>
                                        <div style="font-size: 10px; color: #ccc;">Tổng</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="background: #333; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <h4 style="color: #ffc107; margin-bottom: 10px;">Lịch sử đổi điểm (${data.exchangeHistory.length} lần)</h4>
                                <div style="max-height: 200px; overflow-y: auto;">
                    `;
                    
                    if (data.exchangeHistory.length > 0) {
                        data.exchangeHistory.forEach((exchange, index) => {
                            detailsHtml += `
                                <div style="background: rgba(255,193,7,0.1); border: 1px solid rgba(255,193,7,0.2); border-radius: 6px; padding: 10px; margin-bottom: 8px; font-size: 12px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span style="color: #ffc107; font-weight: 600;">${exchange.timestamp}</span>
                                        <span style="color: #ccc;">${exchange.exchangeType}</span>
                                    </div>
                                    <div style="color: #ccc; margin-bottom: 8px;">
                                        Đã đổi ${exchange.pointsExchanged} điểm → Nhận ${exchange.accountsReceived} tài khoản
                                    </div>
                                    ${exchange.accountText ? `<button class='btn btnSecondary' style='font-size: 10px; padding: 4px 8px; margin-top: 5px;' onclick='showAccountTextPopup(${JSON.stringify(exchange.accountText)})'>Xem tài khoản</button>` : ''}
                                </div>
                            `;
                        });
                    } else {
                        detailsHtml += '<div style="color: #888; text-align: center; padding: 20px;">Chưa có lịch sử đổi điểm</div>';
                    }
                    
                    detailsHtml += `
                                </div>
                            </div>
                            
                            <div style="background: #333; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <h4 style="color: #ffc107; margin-bottom: 10px;">Lịch sử hoạt động (${data.logs.length} lần)</h4>
                                <div style="max-height: 200px; overflow-y: auto;">
                    `;
                    
                    if (data.logs.length > 0) {
                        data.logs.forEach(log => {
                            const bypassClass = log.bypassDetected ? 'suspicious' : 'normal';
                            detailsHtml += `
                                <div style="background: #444; border: 1px solid #555; border-radius: 6px; padding: 10px; margin-bottom: 8px; font-size: 11px; ${log.bypassDetected ? 'border-left: 4px solid #ff4444;' : ''}">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span style="background: #666; color: #fff; padding: 2px 6px; border-radius: 3px; font-size: 10px;">${log.site}</span>
                                        <span style="color: #888; font-size: 10px;">${log.timestamp}</span>
                                    </div>
                                    <div style="color: #ccc; line-height: 1.4;">
                                        <i class="fas fa-globe"></i> IP: ${log.ip}<br>
                                        <i class="fas fa-browser"></i> Browser: ${log.browser}<br>
                                        <i class="fas fa-clock"></i> Time diff: 
                                        <span style="display: inline-block; padding: 2px 6px; border-radius: 3px; font-weight: 600; font-size: 10px; background: ${log.bypassDetected ? '#ff4444' : '#28a745'}; color: white;">
                                            ${log.timeDiff}s
                                        </span>
                                        ${log.bypassDetected ? '<br><strong style="color: #ff4444;"><i class="fas fa-exclamation-triangle"></i> PHÁT HIỆN BYPASS</strong>' : ''}
                                    </div>
                                </div>
                            `;
                        });
                    } else {
                        detailsHtml += '<div style="color: #888; text-align: center; padding: 20px;">Chưa có lịch sử hoạt động</div>';
                    }
                    
                    detailsHtml += `
                                </div>
                            </div>
                        </div>
                    `;
                    
                    showPopup(
                        'Chi tiết người dùng',
                        detailsHtml,
                        '👤',
                        () => document.getElementById('popupOverlay').style.display = 'none',
                        null
                    );
                })
                .catch(error => {
                    showPopup(
                        'Lỗ i',
                        'Không thể tải chi tiết người dùng: ' + error.message,
                        '❌',
                        null,
                        null
                    );
                });
        }
        
        function showAccountTextPopup(accountText) {
            showPopup(
                'Tài khoản đã đổi',
                `<div style="background: #333; padding: 15px; border-radius: 8px; font-family: monospace; white-space: pre-wrap; text-align: left; max-height: 300px; overflow-y: auto;">${accountText}</div>`,
                '📋',
                null,
                null,
                [{ text: 'Đóng', class: 'cancel', onclick: () => document.getElementById('popupOverlay').style.display = 'none' }]
            );
        }
        
        function showBanHistoryPopup(userId) {
            fetch(`/user-details?userId=${userId}`)
                .then(response => response.json())
                .then(data => {
                    let historyHtml = `
                        <div style="text-align: left;">
                            <h3 style="color: #fff; margin-bottom: 15px;">Lịch sử cấm - ${data.user.displayName}</h3>
                            <div style="max-height: 400px; overflow-y: auto;">
                    `;
                    
                    if (data.banHistory && data.banHistory.length > 0) {
                        data.banHistory.forEach((ban, index) => {
                            const isActive = ban.active ? 'Đang hoạt động' : 'Đã hết hạn';
                            const statusColor = ban.active ? '#ff4444' : '#888';
                            
                            historyHtml += `
                                <div style="background: rgba(255,68,68,0.1); border: 1px solid rgba(255,68,68,0.2); border-radius: 8px; padding: 15px; margin-bottom: 10px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                        <span style="color: #ff9999; font-weight: 600;">${ban.banTime}</span>
                                        <span style="color: ${statusColor}; font-size: 12px;">${isActive}</span>
                                    </div>
                                    <div style="color: #ccc; margin-bottom: 8px;">
                                        <strong>Lý do:</strong> ${ban.reason}
                                    </div>
                                    <div style="color: #ccc; margin-bottom: 8px;">
                                        <strong>Thời hạn:</strong> ${ban.duration === -1 ? 'Vĩnh viễn' : ban.duration + ' ngày'}
                                    </div>
                                    ${ban.banEnd ? `<div style="color: #ccc;"><strong>Kết thúc:</strong> ${new Date(ban.banEnd).toLocaleString('vi-VN')}</div>` : ''}
                                </div>
                            `;
                        });
                    } else {
                        historyHtml += '<div style="color: #888; text-align: center; padding: 20px;">Không có lịch sử cấm</div>';
                    }
                    
                    historyHtml += `
                            </div>
                        </div>
                    `;
                    
                    showPopup(
                        'Lịch sử cấm',
                        historyHtml,
                        '📋',
                        null,
                        null,
                        [{ text: 'Đóng', class: 'cancel', onclick: () => document.getElementById('popupOverlay').style.display = 'none' }]
                    );
                })
                .catch(error => {
                    showPopup(
                        'Lỗi',
                        'Không thể tải lịch sử cấm: ' + error.message,
                        '❌',
                        null,
                        null
                    );
                });
        }
        
        function showDeleteBanHistoryPopup(userId) {
            showPopup(
                'Xác nhận xóa lịch sử cấm',
                'Bạn có chắc chắn muốn xóa toàn bộ lịch sử cấm của người dùng này không? Hành động này không thể hoàn tác.',
                '🗑️',
                () => deleteBanHistory(userId),
                null
            );
        }
        
        function banUser(userId, duration) {
            document.getElementById('popupOverlay').style.display = 'none';
            
            fetch('/ban', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userId: userId,
                    duration: duration
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const durationText = duration === -1 ? 'vĩnh viễn' : duration + ' ngày';
                    showPopup(
                        'Thành công',
                        `Đã cấm người dùng trong ${durationText}. Trang sẽ được tải lại.`,
                        '✅',
                        () => location.reload(),
                        null,
                        [{ text: 'OK', class: 'confirm', onclick: () => location.reload() }]
                    );
                } else {
                    showPopup(
                        'Lỗi',
                        'Không thể cấm người dùng: ' + (data.error || 'Lỗi không xác định'),
                        '❌',
                        null,
                        null
                    );
                }
            })
            .catch(error => {
                showPopup(
                    'Lỗi',
                    'Lỗi kết nối: ' + error.message,
                    '❌',
                    null,
                    null
                );
            });
        }
        
        function unbanUser(userId) {
            fetch('/unban', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userId: userId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showPopup(
                        'Thành công',
                        'Đã bỏ cấm người dùng thành công. Trang sẽ được tải lại.',
                        '✅',
                        () => location.reload(),
                        null,
                        [{ text: 'OK', class: 'confirm', onclick: () => location.reload() }]
                    );
                } else {
                    showPopup(
                        'Lỗi',
                        'Không thể bỏ cấm người dùng: ' + (data.error || 'Lỗi không xác định'),
                        '❌',
                        null,
                        null
                    );
                }
            })
            .catch(error => {
                showPopup(
                    'Lỗi',
                    'Lỗi kết nối: ' + error.message,
                    '❌',
                    null,
                    null
                );
            });
        }
        
        function deleteBanHistory(userId) {
            fetch('/delete-ban-history', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    userId: userId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showPopup(
                        'Thành công',
                        'Đã xóa lịch sử cấm thành công. Trang sẽ được tải lại.',
                        '✅',
                        () => location.reload(),
                        null,
                        [{ text: 'OK', class: 'confirm', onclick: () => location.reload() }]
                    );
                } else {
                    showPopup(
                        'Lỗi',
                        'Không thể xóa lịch sử cấm: ' + (data.error || 'Lỗi không xác định'),
                        '❌',
                        null,
                        null
                    );
                }
            })
            .catch(error => {
                showPopup(
                    'Lỗi',
                    'Lỗi kết nối: ' + error.message,
                    '❌',
                    null,
                    null
                );
            });
        }
        
        document.getElementById('searchBox').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const userCards = document.querySelectorAll('.userCard, .exchangeCard, .bannedCard');
            
            userCards.forEach(card => {
                const username = card.getAttribute('data-user') || '';
                const displayName = card.querySelector('.userInfo h3')?.textContent.toLowerCase() || '';
                const userId = card.querySelector('.userInfo p:last-child')?.textContent.toLowerCase() || '';
                
                if (username.includes(searchTerm) || displayName.includes(searchTerm) || userId.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

    </script>
</body>
</html>
