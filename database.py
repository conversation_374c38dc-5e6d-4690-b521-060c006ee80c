import mysql.connector
from mysql.connector import Error
import json
from datetime import datetime, timezone, timedelta
import logging

VIETNAM_TZ = timezone(timedelta(hours=7))

DB_CONFIG = {
    'host': '***********',
    'user': 'ssmljmb_trz',
    'password': 'AdezAdezAdez123',
    'database': 'ssmljmb_BotDiscord',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci',
    'autocommit': False,
    'connection_timeout': 10,
    'pool_reset_session': True,
    'pool_size': 5,
    'pool_name': 'kingbot_pool'
}

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self):
        self.connection = None
        self.connect()
        self.createTables()
    
    def connect(self):
        try:
            # Đóng connection cũ nếu có
            if self.connection:
                try:
                    self.connection.close()
                except:
                    pass

            # Tạo connection mới
            self.connection = mysql.connector.connect(**DB_CONFIG)
            if self.connection.is_connected():
                logger.info("[ DATABASE ] CONNECT SUCCESS")
                # Set session variables for better stability
                cursor = self.connection.cursor()
                cursor.execute("SET SESSION wait_timeout = 28800")  # 8 hours
                cursor.execute("SET SESSION interactive_timeout = 28800")
                cursor.close()
        except Error as e:
            logger.error(f"[ DATABASE ] CONNECT FAILED: {e}")
            raise e
    
    def ensureConnection(self):
        try:
            if self.connection is None or not self.connection.is_connected():
                self.connect()
        except Exception as e:
            logger.warning(f"Connection check failed: {e}, reconnecting...")
            self.connect()
    
    def fetchone(self, query, params=None, retry_count=0):
        max_retries = 3

        try:
            self.ensureConnection()
            cursor = self.connection.cursor()
            try:
                cursor.execute(query, params or ())
                return cursor.fetchone()
            finally:
                cursor.close()
        except Error as e:
            error_msg = str(e)
            logger.error(f"[ ERROR DATABASE ] FETCHONE: {error_msg}")

            # Kiểm tra nếu là lỗi connection và còn retry
            if ("Lost connection" in error_msg or "MySQL server has gone away" in error_msg) and retry_count < max_retries:
                logger.warning(f"[ DATABASE ] Connection lost, retrying fetchone... ({retry_count + 1}/{max_retries})")
                # Reconnect và retry
                self.connect()
                return self.fetchone(query, params, retry_count + 1)
            else:
                raise e
    
    def executeQuery(self, query, params=None, fetch=False, retry_count=0):
        max_retries = 3

        try:
            self.ensureConnection()
            cursor = self.connection.cursor(dictionary=True)
            try:
                cursor.execute(query, params or ())
                if fetch:
                    result = cursor.fetchall()
                    return result
                else:
                    self.connection.commit()
                    return cursor.rowcount
            finally:
                cursor.close()
        except Error as e:
            error_msg = str(e)
            logger.error(f"[ ERROR DATABASE ] QUERY: {error_msg}")

            # Kiểm tra nếu là lỗi connection và còn retry
            if ("Lost connection" in error_msg or "MySQL server has gone away" in error_msg) and retry_count < max_retries:
                logger.warning(f"[ DATABASE ] Connection lost, retrying... ({retry_count + 1}/{max_retries})")
                try:
                    self.connection.rollback()
                except:
                    pass
                # Reconnect và retry
                self.connect()
                return self.executeQuery(query, params, fetch, retry_count + 1)
            else:
                try:
                    self.connection.rollback()
                except:
                    pass
                raise e
    
    def createTables(self):
        tables = {
            'USERS_DATA': '''
                CREATE TABLE IF NOT EXISTS USERS_DATA (
                    USER_ID VARCHAR(50) PRIMARY KEY,
                    USER_NAME VARCHAR(100),
                    DISPLAY_NAME VARCHAR(100),
                    AVATAR_URL TEXT,
                    YEUMONEY_POINTS DECIMAL(10,2) DEFAULT 0,
                    YEUMONEY_DAILY_COUNT INT DEFAULT 0,
                    TOTAL_POINTS DECIMAL(10,2) DEFAULT 0,
                    REGISTERED_IP VARCHAR(45),
                    YEUMONEY_MODE INT DEFAULT 1,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ''',
            'USER_LOGS': '''
                CREATE TABLE IF NOT EXISTS USER_LOGS (
                    LOG_ID INT AUTO_INCREMENT PRIMARY KEY,
                    USER_ID VARCHAR(50),
                    SITE_NAME VARCHAR(50),
                    IP_ADDRESS VARCHAR(45),
                    USER_AGENT TEXT,
                    BROWSER_INFO VARCHAR(200),
                    TIME_DIFF INT,
                    IS_SUSPICIOUS BOOLEAN DEFAULT FALSE,
                    BYPASS_DETECTED BOOLEAN DEFAULT FALSE,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX IDX_USER_ID (USER_ID),
                    INDEX IDX_CREATED_AT (CREATED_AT)
                )
            ''',
            'BANNED_USERS': '''
                CREATE TABLE IF NOT EXISTS BANNED_USERS (
                    BAN_ID INT AUTO_INCREMENT PRIMARY KEY,
                    USER_ID VARCHAR(50),
                    BAN_COUNT INT DEFAULT 1,
                    BAN_REASON TEXT,
                    BAN_DURATION INT,
                    BAN_END TIMESTAMP NULL,
                    IS_ACTIVE BOOLEAN DEFAULT TRUE,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX IDX_USER_ID (USER_ID),
                    INDEX IDX_IS_ACTIVE (IS_ACTIVE)
                )
            ''',
            'EXCHANGE_HISTORY': '''
                CREATE TABLE IF NOT EXISTS EXCHANGE_HISTORY (
                    EXCHANGE_ID INT AUTO_INCREMENT PRIMARY KEY,
                    USER_ID VARCHAR(50),
                    POINTS_EXCHANGED DECIMAL(10,2),
                    ACCOUNTS_RECEIVED INT,
                    EXCHANGE_TYPE VARCHAR(50) DEFAULT 'manual',
                    ACCOUNT_TEXT TEXT,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX IDX_USER_ID (USER_ID),
                    INDEX IDX_CREATED_AT (CREATED_AT)
                )
            ''',
            'TASK_HISTORY': '''
                CREATE TABLE IF NOT EXISTS TASK_HISTORY (
                    TASK_ID INT AUTO_INCREMENT PRIMARY KEY,
                    USER_ID VARCHAR(50),
                    TASK_TYPE VARCHAR(50),
                    ACCOUNTS_RECEIVED INT,
                    ACCOUNT_TEXT TEXT,
                    ACTION_TYPE VARCHAR(50) DEFAULT 'task_completion',
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX IDX_USER_ID (USER_ID),
                    INDEX IDX_CREATED_AT (CREATED_AT)
                )
            ''',
            'TOKEN_DATA': '''
                CREATE TABLE IF NOT EXISTS TOKEN_DATA (
                    TOKEN_ID INT AUTO_INCREMENT PRIMARY KEY,
                    TOKEN_VALUE VARCHAR(500),
                    TOKEN_TYPE ENUM('used', 'url') DEFAULT 'used',
                    URL_VALUE TEXT,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY UNIQUE_TOKEN_TYPE (TOKEN_VALUE, TOKEN_TYPE),
                    INDEX IDX_TOKEN (TOKEN_VALUE),
                    INDEX IDX_TOKEN_TYPE (TOKEN_TYPE)
                )
            ''',
            'LOCKED_CHANNELS': '''
                CREATE TABLE IF NOT EXISTS LOCKED_CHANNELS (
                    CHANNEL_ID VARCHAR(50) PRIMARY KEY,
                    LOCKED_BY VARCHAR(100),
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            'TASK_LINKS': '''
                CREATE TABLE IF NOT EXISTS TASK_LINKS (
                    LINK_ID INT AUTO_INCREMENT PRIMARY KEY,
                    USER_ID VARCHAR(50),
                    TASK_TYPE VARCHAR(50),
                    TASK_LINK TEXT,
                    DAY_KEY VARCHAR(20),
                    PROGRESS_MILESTONE INT DEFAULT 0,
                    IS_USED BOOLEAN DEFAULT FALSE,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX IDX_USER_TASK (USER_ID, TASK_TYPE),
                    INDEX IDX_DAY (DAY_KEY)
                )
            ''',
            'CTV_USERS': '''
                CREATE TABLE IF NOT EXISTS CTV_USERS (
                    USER_ID VARCHAR(50) PRIMARY KEY,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            'CTV_POINT_LOGS': '''
                CREATE TABLE IF NOT EXISTS CTV_POINT_LOGS (
                    ID INT AUTO_INCREMENT PRIMARY KEY,
                    CTV_USER_ID VARCHAR(50) NOT NULL,
                    TARGET_USER_ID VARCHAR(50) NOT NULL,
                    POINTS_ADDED DECIMAL(10,2) NOT NULL,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_ctv_user (CTV_USER_ID),
                    INDEX idx_ctv_target (CTV_USER_ID, TARGET_USER_ID),
                    INDEX idx_created_at (CREATED_AT)
                )
            ''',
            'RESET_COIN_SCHEDULE': '''
                CREATE TABLE IF NOT EXISTS RESET_COIN_SCHEDULE (
                    ID INT PRIMARY KEY DEFAULT 1,
                    ENABLED BOOLEAN DEFAULT FALSE,
                    DAY_OF_WEEK INT DEFAULT 1,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ''',
            'DM_QUEUE': '''
                CREATE TABLE IF NOT EXISTS DM_QUEUE (
                    QUEUE_ID INT AUTO_INCREMENT PRIMARY KEY,
                    USER_ID VARCHAR(50),
                    MESSAGE_CONTENT TEXT,
                    FILE_PATH VARCHAR(255),
                    IS_PROCESSED BOOLEAN DEFAULT FALSE,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX IDX_PROCESSED (IS_PROCESSED),
                    INDEX IDX_USER_ID (USER_ID)
                )
            ''',
            'YEUMONEY_PROGRESS': '''
                CREATE TABLE IF NOT EXISTS YEUMONEY_PROGRESS (
                    PROGRESS_ID INT AUTO_INCREMENT PRIMARY KEY,
                    USER_ID VARCHAR(50),
                    DAY_KEY VARCHAR(20),
                    PROGRESS_LEVEL INT DEFAULT 0,
                    REDIRECT_TOKEN VARCHAR(500),
                    IS_LOCKED BOOLEAN DEFAULT FALSE,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY UNIQUE_USER_DAY (USER_ID, DAY_KEY),
                    INDEX IDX_USER_ID (USER_ID),
                    INDEX IDX_DAY (DAY_KEY)
                )
            ''',
            'BOT_ADMINS': '''
                CREATE TABLE IF NOT EXISTS BOT_ADMINS (
                    ADMIN_ID INT AUTO_INCREMENT PRIMARY KEY,
                    USER_ID VARCHAR(50) UNIQUE,
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            'DAILY_TASKS': '''
                CREATE TABLE IF NOT EXISTS DAILY_TASKS (
                    TASK_ID INT AUTO_INCREMENT PRIMARY KEY,
                    USER_ID VARCHAR(50),
                    TASK_TYPE VARCHAR(50),
                    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX IDX_USER_TASK (USER_ID, TASK_TYPE),
                    INDEX IDX_DATE (CREATED_AT)
                )
            '''
        }
        
        for tableName, query in tables.items():
            try:
                self.executeQuery(query)
                logger.info(f"[ LOAD MySQL] {tableName} CREATE & CHECK")
            except Error as e:
                logger.error(f"[ ERROR MySQL] {tableName}: {e}")
        self.addMissingColumns()

    def addMissingColumns(self):
        try:
            self.executeQuery('''
                ALTER TABLE USERS_DATA 
                ADD COLUMN IF NOT EXISTS REGISTERED_IP VARCHAR(45)
            ''')
            self.executeQuery('''
                ALTER TABLE USERS_DATA
                ADD COLUMN IF NOT EXISTS YEUMONEY_MODE INT DEFAULT 1
            ''')
            
            self.executeQuery('''
                ALTER TABLE USERS_DATA 
                MODIFY COLUMN YEUMONEY_POINTS DECIMAL(10,2) DEFAULT 0
            ''')
            
            self.executeQuery('''
                ALTER TABLE USERS_DATA 
                MODIFY COLUMN TOTAL_POINTS DECIMAL(10,2) DEFAULT 0
            ''')
            
            self.executeQuery('''
                ALTER TABLE EXCHANGE_HISTORY
                MODIFY COLUMN POINTS_EXCHANGED DECIMAL(10,2)
            ''')

            self.executeQuery('''
                ALTER TABLE TASK_LINKS
                ADD COLUMN IF NOT EXISTS IS_USED BOOLEAN DEFAULT FALSE
            ''')

            self.executeQuery('''
                UPDATE USERS_DATA
                SET YEUMONEY_MODE = 1
                WHERE YEUMONEY_MODE IS NULL OR YEUMONEY_MODE != 1
            ''')

            logger.info("[ LOAD MySQL] Missing columns added successfully")
        except Error as e:
            logger.error(f"[ ERROR MySQL] Adding missing columns: {e}")

db = DatabaseManager()
