from Crypto.Cipher import <PERSON><PERSON><PERSON><PERSON>
from Crypto.Random import get_random_bytes
from hashlib import sha256
from base64 import b64encode, b64decode
import jwt
import time

class CryptoUtils:
    rawKey = b"Trz [2,5,0,3,2,0,1,0,0,1,0,2,3,0,5,2,0,0,0,0,0,9,8,2,8,5,9,1]"
    key = sha256(rawKey).digest()
    jwtSecret = "Trz [7,3,8,8,5,7,9,6,2,5,4,5,0,1,2,5,6,8,7,3,2,8,1,7,3,0,4,0]"
    
    @staticmethod
    def encrypt(data):
        nonce = get_random_bytes(8)
        cipher = ChaCha20.new(key=CryptoUtils.key, nonce=nonce)
        encrypted = cipher.encrypt(data.encode())
        return b64encode(nonce + encrypted).decode()
    
    @staticmethod
    def decrypt(encoded):
        raw = b64decode(encoded)
        nonce, encrypted = raw[:8], raw[8:]
        cipher = ChaCha20.new(key=CryptoUtils.key, nonce=nonce)
        return cipher.decrypt(encrypted).decode()
    
    @staticmethod
    def createJwt(encrypted):
        payload = {
            "data": encrypted,
            "iat": int(time.time())
        }
        return jwt.encode(payload, CryptoUtils.jwtSecret, algorithm="HS256")
    
    @staticmethod
    def decodeJwt(token):
        payload = jwt.decode(token, CryptoUtils.jwtSecret, algorithms=["HS256"])
        return CryptoUtils.decrypt(payload["data"])
