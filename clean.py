#!/usr/bin/env python3
"""
Python AST Code Cleaner
Removes comments and converts naming conventions while preserving functionality.
Usage: python clean.py input.py [--style=camelCase|snake_case|UPPER_SNAKE_CASE]
"""

import ast
import sys
import os
import shutil
import argparse
import re
from datetime import datetime
from typing import Dict, Set, Any, Optional


class NameConverter:
    """Handles different naming convention conversions."""
    
    def __init__(self, style: str = "snake_case"):
        self.style = style
        self.converted_names: Dict[str, str] = {}
        # Reserved Python keywords and built-ins to avoid converting
        self.reserved = {
            'False', 'None', 'True', '__debug__', 'and', 'as', 'assert', 'async', 'await',
            'break', 'class', 'continue', 'def', 'del', 'elif', 'else', 'except', 'finally',
            'for', 'from', 'global', 'if', 'import', 'in', 'is', 'lambda', 'nonlocal',
            'not', 'or', 'pass', 'raise', 'return', 'try', 'while', 'with', 'yield',
            # Built-in functions
            'abs', 'all', 'any', 'ascii', 'bin', 'bool', 'bytearray', 'bytes', 'callable',
            'chr', 'classmethod', 'compile', 'complex', 'delattr', 'dict', 'dir', 'divmod',
            'enumerate', 'eval', 'exec', 'filter', 'float', 'format', 'frozenset',
            'getattr', 'globals', 'hasattr', 'hash', 'help', 'hex', 'id', 'input', 'int',
            'isinstance', 'issubclass', 'iter', 'len', 'list', 'locals', 'map', 'max',
            'memoryview', 'min', 'next', 'object', 'oct', 'open', 'ord', 'pow', 'print',
            'property', 'range', 'repr', 'reversed', 'round', 'set', 'setattr', 'slice',
            'sorted', 'staticmethod', 'str', 'sum', 'super', 'tuple', 'type', 'vars', 'zip'
        }
    
    def to_camel_case(self, name: str) -> str:
        """Convert to camelCase."""
        if '_' not in name:
            return name
        components = name.split('_')
        return components[0] + ''.join(word.capitalize() for word in components[1:])
    
    def to_snake_case(self, name: str) -> str:
        """Convert to snake_case."""
        # Insert underscore before uppercase letters (for camelCase to snake_case)
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    def to_upper_snake_case(self, name: str) -> str:
        """Convert to UPPER_SNAKE_CASE."""
        return self.to_snake_case(name).upper()
    
    def convert_name(self, name: str) -> str:
        """Convert name based on selected style."""
        # Skip reserved words, private variables, and special methods
        if (name in self.reserved or 
            name.startswith('__') and name.endswith('__') or
            name.startswith('_')):
            return name
        
        if name in self.converted_names:
            return self.converted_names[name]
        
        if self.style == "camelCase":
            converted = self.to_camel_case(name)
        elif self.style == "snake_case":
            converted = self.to_snake_case(name)
        elif self.style == "UPPER_SNAKE_CASE":
            converted = self.to_upper_snake_case(name)
        else:
            converted = name
        
        self.converted_names[name] = converted
        return converted


class CommentRemover(ast.NodeTransformer):
    """AST transformer to remove comments and convert naming conventions."""
    
    def __init__(self, name_converter: NameConverter):
        self.name_converter = name_converter
        self.defined_names: Set[str] = set()
        
    def visit_Name(self, node):
        """Visit Name nodes to convert identifiers."""
        if isinstance(node.ctx, (ast.Store, ast.Del)):
            # This is a variable being defined/deleted
            self.defined_names.add(node.id)
        
        # Convert the name if it's a user-defined variable
        if node.id in self.defined_names:
            node.id = self.name_converter.convert_name(node.id)
        
        return self.generic_visit(node)
    
    def visit_FunctionDef(self, node):
        """Visit function definitions."""
        # Convert function name
        node.name = self.name_converter.convert_name(node.name)
        
        # Convert parameter names
        for arg in node.args.args:
            arg.arg = self.name_converter.convert_name(arg.arg)
        
        # Handle keyword-only arguments
        for arg in node.args.kwonlyargs:
            arg.arg = self.name_converter.convert_name(arg.arg)
        
        # Handle *args and **kwargs
        if node.args.vararg:
            node.args.vararg.arg = self.name_converter.convert_name(node.args.vararg.arg)
        if node.args.kwarg:
            node.args.kwarg.arg = self.name_converter.convert_name(node.args.kwarg.arg)
        
        return self.generic_visit(node)
    
    def visit_AsyncFunctionDef(self, node):
        """Visit async function definitions."""
        return self.visit_FunctionDef(node)
    
    def visit_ClassDef(self, node):
        """Visit class definitions."""
        # Convert class name
        if self.name_converter.style != "UPPER_SNAKE_CASE":
            # Classes typically use PascalCase, so only convert if not going to UPPER_SNAKE_CASE
            if self.name_converter.style == "camelCase":
                # Convert to PascalCase for classes
                node.name = node.name[0].upper() + self.name_converter.convert_name(node.name)[1:]
            else:
                node.name = self.name_converter.convert_name(node.name)
        
        return self.generic_visit(node)
    
    def visit_Assign(self, node):
        """Visit assignment nodes."""
        # Track assigned variable names
        for target in node.targets:
            if isinstance(target, ast.Name):
                self.defined_names.add(target.id)
        
        return self.generic_visit(node)
    
    def visit_AnnAssign(self, node):
        """Visit annotated assignment nodes."""
        if isinstance(node.target, ast.Name):
            self.defined_names.add(node.target.id)
        
        return self.generic_visit(node)


def create_backup(file_path: str) -> str:
    """Create a backup of the original file."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_{timestamp}"
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"✓ Backup created: {backup_path}")
        return backup_path
    except Exception as e:
        raise Exception(f"Failed to create backup: {e}")


def remove_comments_from_source(source: str) -> str:
    """Remove comments from source code using simple regex."""
    lines = source.split('\n')
    cleaned_lines = []
    
    in_multiline_string = False
    string_delimiter = None
    
    for line in lines:
        cleaned_line = ""
        i = 0
        
        while i < len(line):
            char = line[i]
            
            # Handle string literals
            if char in ['"', "'"]:
                if not in_multiline_string:
                    # Check for triple quotes
                    if i + 2 < len(line) and line[i:i+3] == char * 3:
                        in_multiline_string = True
                        string_delimiter = char * 3
                        cleaned_line += line[i:i+3]
                        i += 3
                        continue
                    else:
                        # Single/double quote string
                        string_delimiter = char
                        in_multiline_string = True
                elif string_delimiter == char * 3 and i + 2 < len(line) and line[i:i+3] == char * 3:
                    # End of triple-quoted string
                    in_multiline_string = False
                    string_delimiter = None
                    cleaned_line += line[i:i+3]
                    i += 3
                    continue
                elif string_delimiter == char and not (i > 0 and line[i-1] == '\\'):
                    # End of single/double quoted string
                    in_multiline_string = False
                    string_delimiter = None
                
                cleaned_line += char
                i += 1
                continue
            
            # If in string, add character as-is
            if in_multiline_string:
                cleaned_line += char
                i += 1
                continue
            
            # Check for comment outside of string
            if char == '#':
                break  # Rest of line is comment
            
            cleaned_line += char
            i += 1
        
        # Remove trailing whitespace but preserve indentation structure
        cleaned_line = cleaned_line.rstrip()
        cleaned_lines.append(cleaned_line)
    
    return '\n'.join(cleaned_lines)


def process_file(file_path: str, style: str = "snake_case") -> bool:
    """Process a Python file to remove comments and convert naming conventions."""
    
    if not os.path.exists(file_path):
        print(f"❌ Error: File '{file_path}' not found.")
        return False
    
    if not file_path.endswith('.py'):
        print(f"❌ Error: '{file_path}' is not a Python file.")
        return False
    
    try:
        # Create backup first
        backup_path = create_backup(file_path)
        
        # Read original file
        with open(file_path, 'r', encoding='utf-8') as f:
            original_source = f.read()
        
        print(f"📝 Processing: {file_path}")
        print(f"🎨 Style: {style}")
        
        # Remove comments first
        source_without_comments = remove_comments_from_source(original_source)
        
        # Parse AST
        try:
            tree = ast.parse(source_without_comments)
        except SyntaxError as e:
            print(f"❌ Syntax error in file: {e}")
            return False
        
        # Transform AST
        name_converter = NameConverter(style)
        transformer = CommentRemover(name_converter)
        transformed_tree = transformer.visit(tree)
        
        # Convert back to source code
        try:
            import astor
            transformed_source = astor.to_source(transformed_tree)
        except ImportError:
            # Fallback: use ast.unparse if available (Python 3.9+)
            try:
                transformed_source = ast.unparse(transformed_tree)
            except AttributeError:
                print("❌ Error: Please install 'astor' package: pip install astor")
                return False
        
        # Write transformed code
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(transformed_source)
        
        print(f"✅ Successfully processed '{file_path}'")
        print(f"📊 Converted {len(name_converter.converted_names)} identifiers")
        
        if name_converter.converted_names:
            print("\n🔄 Name conversions:")
            for old_name, new_name in sorted(name_converter.converted_names.items()):
                if old_name != new_name:
                    print(f"  {old_name} → {new_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        # Restore from backup if something went wrong
        if 'backup_path' in locals():
            try:
                shutil.copy2(backup_path, file_path)
                print(f"🔄 Restored original file from backup")
            except:
                pass
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Remove comments and convert naming conventions in Python files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python clean.py input.py                          # Use snake_case (default)
  python clean.py input.py --style=camelCase       # Convert to camelCase
  python clean.py input.py --style=UPPER_SNAKE_CASE # Convert to UPPER_SNAKE_CASE
        """
    )
    
    parser.add_argument('input_file', help='Input Python file to process')
    parser.add_argument(
        '--style', 
        choices=['camelCase', 'snake_case', 'UPPER_SNAKE_CASE'],
        default='snake_case',
        help='Naming convention style (default: snake_case)'
    )
    
    args = parser.parse_args()
    
    print("🐍 Python AST Code Cleaner")
    print("=" * 40)
    
    success = process_file(args.input_file, args.style)
    
    if success:
        print("\n✅ Processing completed successfully!")
        print("💾 Original file backed up automatically")
    else:
        print("\n❌ Processing failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()