[{"message_num": 1, "raw_bytes_hex": "0a03040208", "raw_bytes_list": [10, 3, 4, 2, 8], "decoded": "{'1': b'\\x04\\x02\\x08'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 2, "raw_bytes_hex": "0a03020a0a", "raw_bytes_list": [10, 3, 2, 10, 10], "decoded": "{'1': b'\\x02\\n\\n'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 3, "raw_bytes_hex": "0a030c0802", "raw_bytes_list": [10, 3, 12, 8, 2], "decoded": "{'1': b'\\x0c\\x08\\x02'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 4, "raw_bytes_hex": "0a03020a02", "raw_bytes_list": [10, 3, 2, 10, 2], "decoded": "{'1': b'\\x02\\n\\x02'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 5, "raw_bytes_hex": "0a030a0202", "raw_bytes_list": [10, 3, 10, 2, 2], "decoded": "{'1': b'\\n\\x02\\x02'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 6, "raw_bytes_hex": "0a030c040c", "raw_bytes_list": [10, 3, 12, 4, 12], "decoded": "{'1': b'\\x0c\\x04\\x0c'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 7, "raw_bytes_hex": "0a030c0206", "raw_bytes_list": [10, 3, 12, 2, 6], "decoded": "{'1': b'\\x0c\\x02\\x06'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 8, "raw_bytes_hex": "0a030a020c", "raw_bytes_list": [10, 3, 10, 2, 12], "decoded": "{'1': b'\\n\\x02\\x0c'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 9, "raw_bytes_hex": "0a0304020a", "raw_bytes_list": [10, 3, 4, 2, 10], "decoded": "{'1': b'\\x04\\x02\\n'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 10, "raw_bytes_hex": "0a03080208", "raw_bytes_list": [10, 3, 8, 2, 8], "decoded": "{'1': b'\\x08\\x02\\x08'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 11, "raw_bytes_hex": "0a0308040c", "raw_bytes_list": [10, 3, 8, 4, 12], "decoded": "{'1': b'\\x08\\x04\\x0c'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 12, "raw_bytes_hex": "0a03020c08", "raw_bytes_list": [10, 3, 2, 12, 8], "decoded": "{'1': b'\\x02\\x0c\\x08'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 13, "raw_bytes_hex": "0a03060406", "raw_bytes_list": [10, 3, 6, 4, 6], "decoded": "{'1': b'\\x06\\x04\\x06'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 14, "raw_bytes_hex": "0a030c0406", "raw_bytes_list": [10, 3, 12, 4, 6], "decoded": "{'1': b'\\x0c\\x04\\x06'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 15, "raw_bytes_hex": "0a030a0a02", "raw_bytes_list": [10, 3, 10, 10, 2], "decoded": "{'1': b'\\n\\n\\x02'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 16, "raw_bytes_hex": "0a03080c06", "raw_bytes_list": [10, 3, 8, 12, 6], "decoded": "{'1': b'\\x08\\x0c\\x06'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 17, "raw_bytes_hex": "0a03060406", "raw_bytes_list": [10, 3, 6, 4, 6], "decoded": "{'1': b'\\x06\\x04\\x06'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 18, "raw_bytes_hex": "0a03020804", "raw_bytes_list": [10, 3, 2, 8, 4], "decoded": "{'1': b'\\x02\\x08\\x04'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 19, "raw_bytes_hex": "0a030a0208", "raw_bytes_list": [10, 3, 10, 2, 8], "decoded": "{'1': b'\\n\\x02\\x08'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 20, "raw_bytes_hex": "0a03060202", "raw_bytes_list": [10, 3, 6, 2, 2], "decoded": "{'1': b'\\x06\\x02\\x02'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 21, "raw_bytes_hex": "0a0306040c", "raw_bytes_list": [10, 3, 6, 4, 12], "decoded": "{'1': b'\\x06\\x04\\x0c'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 22, "raw_bytes_hex": "0a03020604", "raw_bytes_list": [10, 3, 2, 6, 4], "decoded": "{'1': b'\\x02\\x06\\x04'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 23, "raw_bytes_hex": "0a0306080a", "raw_bytes_list": [10, 3, 6, 8, 10], "decoded": "{'1': b'\\x06\\x08\\n'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 24, "raw_bytes_hex": "0a03060602", "raw_bytes_list": [10, 3, 6, 6, 2], "decoded": "{'1': b'\\x06\\x06\\x02'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 25, "raw_bytes_hex": "0a03040a0c", "raw_bytes_list": [10, 3, 4, 10, 12], "decoded": "{'1': b'\\x04\\n\\x0c'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 26, "raw_bytes_hex": "0a030c0804", "raw_bytes_list": [10, 3, 12, 8, 4], "decoded": "{'1': b'\\x0c\\x08\\x04'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 27, "raw_bytes_hex": "0a030c0606", "raw_bytes_list": [10, 3, 12, 6, 6], "decoded": "{'1': b'\\x0c\\x06\\x06'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 28, "raw_bytes_hex": "0a030c0808", "raw_bytes_list": [10, 3, 12, 8, 8], "decoded": "{'1': b'\\x0c\\x08\\x08'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 29, "raw_bytes_hex": "0a0308080a", "raw_bytes_list": [10, 3, 8, 8, 10], "decoded": "{'1': b'\\x08\\x08\\n'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 30, "raw_bytes_hex": "0a030a0402", "raw_bytes_list": [10, 3, 10, 4, 2], "decoded": "{'1': b'\\n\\x04\\x02'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 31, "raw_bytes_hex": "0a030c020a", "raw_bytes_list": [10, 3, 12, 2, 10], "decoded": "{'1': b'\\x0c\\x02\\n'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 32, "raw_bytes_hex": "0a030a040a", "raw_bytes_list": [10, 3, 10, 4, 10], "decoded": "{'1': b'\\n\\x04\\n'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 33, "raw_bytes_hex": "0a030c0402", "raw_bytes_list": [10, 3, 12, 4, 2], "decoded": "{'1': b'\\x0c\\x04\\x02'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 34, "raw_bytes_hex": "0a030c0a04", "raw_bytes_list": [10, 3, 12, 10, 4], "decoded": "{'1': b'\\x0c\\n\\x04'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 35, "raw_bytes_hex": "0a03020c08", "raw_bytes_list": [10, 3, 2, 12, 8], "decoded": "{'1': b'\\x02\\x0c\\x08'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 36, "raw_bytes_hex": "0a030a0206", "raw_bytes_list": [10, 3, 10, 2, 6], "decoded": "{'1': b'\\n\\x02\\x06'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 37, "raw_bytes_hex": "0a03040a0a", "raw_bytes_list": [10, 3, 4, 10, 10], "decoded": "{'1': b'\\x04\\n\\n'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 38, "raw_bytes_hex": "0a03020c06", "raw_bytes_list": [10, 3, 2, 12, 6], "decoded": "{'1': b'\\x02\\x0c\\x06'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 39, "raw_bytes_hex": "0a030c040c", "raw_bytes_list": [10, 3, 12, 4, 12], "decoded": "{'1': b'\\x0c\\x04\\x0c'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 40, "raw_bytes_hex": "0a030c0804", "raw_bytes_list": [10, 3, 12, 8, 4], "decoded": "{'1': b'\\x0c\\x08\\x04'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 41, "raw_bytes_hex": "0a030a0604", "raw_bytes_list": [10, 3, 10, 6, 4], "decoded": "{'1': b'\\n\\x06\\x04'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 42, "raw_bytes_hex": "0a03040a08", "raw_bytes_list": [10, 3, 4, 10, 8], "decoded": "{'1': b'\\x04\\n\\x08'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 43, "raw_bytes_hex": "0a03080c0c", "raw_bytes_list": [10, 3, 8, 12, 12], "decoded": "{'1': b'\\x08\\x0c\\x0c'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 44, "raw_bytes_hex": "0a03060804", "raw_bytes_list": [10, 3, 6, 8, 4], "decoded": "{'1': b'\\x06\\x08\\x04'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 45, "raw_bytes_hex": "0a03080a0a", "raw_bytes_list": [10, 3, 8, 10, 10], "decoded": "{'1': b'\\x08\\n\\n'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 46, "raw_bytes_hex": "0a03040602", "raw_bytes_list": [10, 3, 4, 6, 2], "decoded": "{'1': b'\\x04\\x06\\x02'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 47, "raw_bytes_hex": "0a03080608", "raw_bytes_list": [10, 3, 8, 6, 8], "decoded": "{'1': b'\\x08\\x06\\x08'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 48, "raw_bytes_hex": "0a030a0a08", "raw_bytes_list": [10, 3, 10, 10, 8], "decoded": "{'1': b'\\n\\n\\x08'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 49, "raw_bytes_hex": "0a030c0a08", "raw_bytes_list": [10, 3, 12, 10, 8], "decoded": "{'1': b'\\x0c\\n\\x08'}", "typedef": {"1": {"type": "bytes", "name": ""}}}, {"message_num": 50, "raw_bytes_hex": "0a030c0802", "raw_bytes_list": [10, 3, 12, 8, 2], "decoded": "{'1': b'\\x0c\\x08\\x02'}", "typedef": {"1": {"type": "bytes", "name": ""}}}]