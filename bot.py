import discord
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
from discord import app_commands, Embed
from threading import Thread
import asyncio
import urllib.parse
from utils import Utils
from crypto_utils import CryptoUtils
import requests
import time
import uuid
import aiohttp
import json
from typing import Literal
from asyncio import Lock
import os
import base64
ADMIN_ID = ['1257677398552477736', '1001391086926827570']
cooldowns = {}
HEADERS = {}
userLock = Lock()


def getAllowedChannel(guildId):
    return None


def generateUniqueFileName(userId):
    unique_id = str(uuid.uuid4())
    return f'King_Dev_{unique_id}.txt'


def checkExistingFile(userId, filePath):
    if os.path.exists(filePath):
        existingQueue = Utils.getDmQueueByUser(userId)
        for item in existingQueue:
            if item.get('FILE_PATH') == filePath and item.get('STATUS'
                ) != 'PROCESSED':
                return True
    return False


async def parseLocalstorage(localStorage: str) ->tuple[str | None, str | None]:
    try:
        if localStorage.startswith('https://'):
            async with aiohttp.ClientSession() as session:
                async with session.get(localStorage) as response:
                    response.raise_for_status()
                    content = await response.text()
        else:
            content = localStorage
        try:
            data = json.loads(content)
        except json.JSONDecodeError:
            parts = content.split('|')
            if len(parts) < 3:
                print('❌ Định dạng input không hợp lệ.')
                return None, None
            jsonData = parts[2]
            try:
                data = json.loads(jsonData)
            except json.JSONDecodeError:
                print('❌ JSON trong phần thứ 3 không hợp lệ.')
                return None, None
        if not isinstance(data, dict):
            print('❌ Dữ liệu không phải là một JSON object.')
            return None, None
        mqttData = data.get('UGPHONE-MQTT')
        if isinstance(mqttData, str):
            try:
                mqttData = json.loads(mqttData)
            except json.JSONDecodeError:
                print('❌ mqttData không phải JSON hợp lệ.')
                return None, None
        accessToken = mqttData.get('access_token')
        loginId = mqttData.get('login_id')
        if not accessToken or not loginId:
            print('❌ Không tìm thấy access_token hoặc login_id.')
            return None, None
        return accessToken, loginId
    except Exception as e:
        print(f'❌ Lỗi xử lý dữ liệu: {e}')
        return None, None


@app_commands.command(name='codelogin', description=
    'Lấy đoạn mã để đăng nhập Ugphone')
async def codelogin(interaction: discord.Interaction):
    userId1 = str(interaction.user.id)
    now = time.time()
    if userId1 in cooldowns and now - cooldowns[userId1] < 60:
        remaining = 60 - (now - cooldowns[userId1])
        await interaction.response.send_message(
            f'⏳ Bạn cần đợi {remaining:.2f} giây trước khi dùng lại lệnh này.',
            ephemeral=True, delete_after=5)
        return
    cooldowns[userId1] = now
    allowedChannel = getAllowedChannel(interaction.guild_id
        ) if interaction.guild_id else None
    if allowedChannel and interaction.channel_id != allowedChannel:
        await interaction.response.send_message(
            f'⛔ Lệnh này chỉ được sử dụng tại <#{allowedChannel}>.',
            ephemeral=True)
        return
    codelogin = (
        "var currentLocalStorage = prompt('Nhập localstorage ugphone: ');localStorage.clear();try {  var parse = JSON.parse(currentLocalStorage);  if (parse.hasOwnProperty('userFloatInfo')) {    delete parse.userFloatInfo;  }  for (var name in parse) {    localStorage.setItem(name, parse[name]);  }  window.location.reload();} catch (error) {  console.error('Lỗi khi parse JSON:', error);  alert('JSON không hợp lệ!');}"
        )
    DmsEmbed = discord.Embed(title='📌 **CodeLogin Ugphone!!!**',
        description=codelogin, color=discord.Color.red())
    DmsEmbed.timestamp = discord.utils.utcnow()
    DmsEmbed.set_footer(text=f'User: {interaction.user} | ID: {userId1}')
    await interaction.response.send_message(embed=DmsEmbed, ephemeral=True)


def readCredentials():
    return None, None


ACCESS_TOKEN, LOGIN_ID = readCredentials()
if not ACCESS_TOKEN or not LOGIN_ID:
    print(
        'ℹ️ Bot will continue running, but /auto_ugphone will not work until valid localStorage is provided.'
        )


@commands.guild_only()
@app_commands.command(name='auto_ugphone', description=
    'Auto mua máy từ UGPhone.')
@app_commands.describe(local_storage=
    'Dán localStorage hoặc URL chứa localStorage.', country_code=
    'Chọn quốc gia mua máy (sg, hk, de, us, jp).')
@app_commands.choices(country_code=[app_commands.Choice(name='Singapore',
    value='sg'), app_commands.Choice(name='Hong Kong', value='hk'),
    app_commands.Choice(name='Germany', value='de'), app_commands.Choice(
    name='USA', value='us'), app_commands.Choice(name='Japan', value='jp')])
async def autoUgphone(interaction: discord.Interaction, country_code:
    app_commands.Choice[str], local_storage: str):
    allowedChannel = getAllowedChannel(interaction.guild_id
        ) if interaction.guild_id else None
    if allowedChannel and interaction.channel_id != allowedChannel:
        await interaction.response.send_message(
            f'⛔ Lệnh này chỉ được sử dụng tại <#{allowedChannel}>.',
            ephemeral=True)
        return
    userId1 = str(interaction.user.id)
    now = time.time()
    if userId1 in cooldowns and now - cooldowns[userId1] < 60:
        remaining = 60 - (now - cooldowns[userId1])
        await interaction.response.send_message(
            f'⏳ Bạn cần đợi {remaining:.2f} giây trước khi dùng lại lệnh này.',
            ephemeral=True, delete_after=5)
        return
    cooldowns[userId1] = now
    await interaction.response.defer(thinking=True, ephemeral=True)
    await interaction.followup.send('🔃 Đang chờ đến lượt xử lý của bạn...',
        ephemeral=True)
    async with userLock:
        userId = interaction.user.id
        accessToken, loginId = await parseLocalstorage(local_storage)
        if not accessToken or not loginId:
            await interaction.followup.send('❌ Không đọc được localStorage!',
                ephemeral=True)
            return
        global HEADERS
        HEADERS['access-token'] = accessToken
        HEADERS['login-id'] = loginId
        urlAuto = 'https://tool.kingcrtis1.workers.dev/buy'
        payload = {'content': local_storage, 'country_code': country_code.value
            }
        success = False
        last_response = None
        for attempt in range(3):
            try:
                req = requests.post(urlAuto, json=payload, timeout=20).json()
            except Exception as e:
                req = {}
            if req.get('status') == 'success':
                orderId = req.get('order_id')
                embed = discord.Embed(title='✅ Đổi thiết bị thành công!',
                    description=
                    f"""**ID tài khoản của bạn:** `{loginId}`
**Order ID:** `{orderId}`

Bạn đã đổi thiết bị thành công. Chúc bạn sử dụng vui vẻ!"""
                    , color=discord.Color.green())
                embed.set_footer(text=
                    f'User: {interaction.user} | ID: {userId}')
                embed.timestamp = discord.utils.utcnow()
                codelogin = f"""
                    var currentLocalStorage = {local_storage};
                    localStorage.clear();
                    try {{
                       var parse = currentLocalStorage;
                        if (parse.hasOwnProperty('userFloatInfo')) {{
                            delete parse.userFloatInfo;
                        }}
                        for (var name in parse) {{
                            localStorage.setItem(name, parse[name]);
                        }}
                        window.location.reload();
                    }} catch (error) {{
                        console.error('Lỗi khi parse JSON:', error);
                        alert('JSON không hợp lệ!');
                    }}
                    """
                codelogin_b64 = base64.b64encode(codelogin.encode('utf-8')
                    ).decode('utf-8')
                run_code = f"eval(atob('{codelogin_b64}'));"
                embed2 = discord.Embed(title=
                    '✅ Dán vào console để tự động đăng nhập!', description=
                    f'{run_code}', color=discord.Color.green())
                embed.set_footer(text=
                    f'User: {interaction.user} | ID: {userId}')
                embed.timestamp = discord.utils.utcnow()
                await interaction.followup.send(embed=embed)
                await interaction.user.send(embed=embed2)
                success = True
                break
            else:
                last_response = req
                if attempt < 2:
                    await asyncio.sleep(0.5)
        if not success:
            await interaction.followup.send(
                '❌ Mua thất bại vui lòng thử lại sau.', ephemeral=True)


class DiscordBot:

    def __init__(self):
        self.intents = discord.Intents.default()
        self.intents.message_content = True
        self.activity = discord.Game(name='yeu em vai l')
        self.bot = commands.Bot(command_prefix='?', intents=self.intents,
            activity=self.activity)
        self.setupBot()
        try:
            self.bot.tree.add_command(autoUgphone)
            self.bot.tree.add_command(codelogin)
        except Exception as e:
            print(f'[AUTO_UGPHONE] Command registration error: {e}')

    def setupBot(self):

        @self.bot.event
        async def on_ready():
            print(f'Bot {self.bot.user} is ready!')
            try:
                synced = await self.bot.tree.sync()
                print(f'[AUTO SYNC] Đã sync {len(synced)} lệnh slash toàn cục!'
                    )
            except Exception as e:
                print(f'[AUTO SYNC] Lỗi khi sync slash commands: {e}')
            self.bot.loop.create_task(self.dmQueueWorker())
            self.bot.loop.create_task(self.dailyResetWorker())

        @self.bot.command()
        async def key(ctx):
            await ctx.message.delete()
            embed = Embed(title='🔑 King Bot!', description=
                """👉 **Nếu muốn lấy tài khoản**
⚡ **Hãy dùng lệnh:** `?nv` hoặc `/nhiemvu` để nhận tài khoản!"""
                , color=discord.Color.gold())
            user = ctx.author
            tag = str(user)
            embed.set_footer(text=
                f'KingDev | User: {tag} | Hỗ trợ Bot https://discord.gg/4UB9RuJqSn'
                , icon_url=
                'https://cdn.discordapp.com/avatars/1001391086926827570/1d2c01bbc4f36a672155ed8364b85f32.png?size=4096'
                )
            await ctx.send(embed=embed)

        @self.bot.command()
        async def ug(ctx):
            await ctx.message.delete()
            embed = Embed(title='🔑 King Bot!', description=
                """👉 **Nếu muốn lấy tài khoản**
⚡ **Hãy dùng lệnh:** `?nv` hoặc `/nhiemvu` để nhận tài khoản!"""
                , color=discord.Color.gold())
            user = ctx.author
            tag = str(user)
            embed.set_footer(text=
                f'KingDev | User: {tag} | Hỗ trợ Bot https://discord.gg/4UB9RuJqSn'
                , icon_url=
                'https://cdn.discordapp.com/avatars/1001391086926827570/1d2c01bbc4f36a672155ed8364b85f32.png?size=4096'
                )
            await ctx.send(embed=embed)

        @self.bot.command()
        async def nv(ctx):
            userId = str(ctx.author.id)
            if Utils.isChannelLocked(ctx.channel.id):
                embed = discord.Embed(title='🔒 Channel Bị Khóa',
                    description=
                    'Bot không hoạt động trên channel này. Vui lòng liên hệ admin để mở khóa.'
                    , color=discord.Color.red())
                await ctx.send(embed=embed)
                return
            if Utils.isUserBanned(userId):
                banHistory = Utils.getUserBanHistory(userId)
                if banHistory:
                    ban = banHistory[0]
                    reason = ban.get('BAN_REASON', 'Không rõ')
                    duration = ban.get('BAN_DURATION', -1)
                    banEnd = ban.get('BAN_END')
                    if duration == -1:
                        timeStr = 'Vĩnh viễn'
                    elif banEnd:
                        from datetime import datetime
                        now = datetime.now(banEnd.tzinfo) if hasattr(banEnd,
                            'tzinfo') else datetime.now()
                        remaining = banEnd - now
                        if remaining.total_seconds() > 0:
                            days = remaining.days
                            hours = remaining.seconds // 3600
                            minutes = remaining.seconds % 3600 // 60
                            timeStr = (
                                f'Còn {days} ngày {hours} giờ {minutes} phút')
                        else:
                            timeStr = 'Đã hết hạn'
                    else:
                        timeStr = f'{duration} ngày'
                else:
                    reason = 'Không rõ'
                    timeStr = 'Không rõ'
                embed = discord.Embed(title='🚫 Bạn đã bị cấm', description=
                    f"""Bạn không thể sử dụng bot này do vi phạm quy định.

**Lý do:** {reason}
**Thời hạn:** {timeStr}"""
                    , color=discord.Color.red())
                await ctx.send(embed=embed)
                return
            user = ctx.author
            userInfo = {'username': user.name, 'displayName': user.
                display_name or user.name, 'avatar': str(user.avatar.url) if
                user.avatar else None}
            Utils.getOrCreateUser(userId, userInfo)
            totalPoints = Utils.getTotalPoints(userId)
            nowStr = Utils.getFormattedDateTime()
            embed = discord.Embed(title='🎯 ᴅᴀɴʜ ꜱᴀ́ᴄʜ ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣',
                description=
                f"""**📝 ʜᴜ̛ᴏ̛́ɴɢ ᴅᴀ̂̃ɴ**
ɴʜᴀ̂́ɴ ᴠᴀ̀ᴏ ɴᴜ́ᴛ ʙᴇ̂ɴ ᴅᴜ̛ᴏ̛́ɪ ᴅᴇ̂̉ ᴄʜᴏ̣ɴ ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣ ᴠᴀ̀ ʟᴀ̀ᴍ ᴛʜᴇᴏ ʜᴜ̛ᴏ̛́ɴɢ ᴅᴀ̂̃ɴ.

**📌 ʟᴜ̛ᴜ ʏ́**
ᴄʜɪ̉ ʟᴀ̀ᴍ ᴄᴀ́ᴄ ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣ ᴄʜᴜ̛ᴀ ʜᴏᴀ̀ɴ ᴛʜᴀ̀ɴʜ ᴅᴇ̂̉ ɴʜᴀ̣̂ɴ ᴅɪᴇ̂̉ᴍ.

**⚠️ ɴᴏᴛᴇ**
* Nếu là lần đầu trong ngày thì bạn phải vượt LinkNgon trước để mở vượt TrafficUser và Yeumoney *

**💰 ᴅɪᴇ̂̉ᴍ ʜɪᴇ̣̂ɴ ᴛᴀ̣ɪ**
`{totalPoints} ᴅɪᴇ̂̉ᴍ`

`{nowStr}` - 👤 {user.display_name or user.name}"""
                , color=discord.Color.blue())
            embed.set_thumbnail(url='http://ltruowng.space/king.png')
            await ctx.reply(embed=embed, view=TaskView(self.bot, userId))

        @self.bot.tree.command(name='nhiemvu', description=
            'ʜɪᴇ̣̂ɴ ᴅᴀɴʜ ꜱᴀ́ᴄʜ ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣')
        async def nhiemvuSlash(interaction: discord.Interaction):
            userId = str(interaction.user.id)
            if Utils.isChannelLocked(interaction.channel.id):
                embed = discord.Embed(title='🔒 Channel Bị Khóa',
                    description=
                    'Bot không hoạt động trên channel này. Vui lòng liên hệ admin để mở khóa.'
                    , color=discord.Color.red())
                await interaction.response.send_message(embed=embed,
                    ephemeral=True)
                return
            if Utils.isUserBanned(userId):
                banHistory = Utils.getUserBanHistory(userId)
                if banHistory:
                    ban = banHistory[0]
                    reason = ban.get('BAN_REASON', 'Không rõ')
                    duration = ban.get('BAN_DURATION', -1)
                    banEnd = ban.get('BAN_END')
                    if duration == -1:
                        timeStr = 'Vĩnh viễn'
                    elif banEnd:
                        from datetime import datetime
                        now = datetime.now(banEnd.tzinfo) if hasattr(banEnd,
                            'tzinfo') else datetime.now()
                        remaining = banEnd - now
                        if remaining.total_seconds() > 0:
                            days = remaining.days
                            hours = remaining.seconds // 3600
                            minutes = remaining.seconds % 3600 // 60
                            timeStr = (
                                f'Còn {days} ngày {hours} giờ {minutes} phút')
                        else:
                            timeStr = 'Đã hết hạn'
                    else:
                        timeStr = f'{duration} ngày'
                else:
                    reason = 'Không rõ'
                    timeStr = 'Không rõ'
                embed = discord.Embed(title='🚫 Bạn đã bị cấm', description=
                    f"""Bạn không thể sử dụng bot này do vi phạm quy định.

**Lý do:** {reason}
**Thời hạn:** {timeStr}"""
                    , color=discord.Color.red())
                await interaction.response.send_message(embed=embed,
                    ephemeral=True)
                return
            user = interaction.user
            userInfo = {'username': user.name, 'displayName': user.
                display_name or user.name, 'avatar': str(user.avatar.url) if
                user.avatar else None}
            Utils.getOrCreateUser(userId, userInfo)
            totalPoints = Utils.getTotalPoints(userId)
            nowStr = Utils.getFormattedDateTime()
            embed = discord.Embed(title='🎯 ᴅᴀɴʜ ꜱᴀ́ᴄʜ ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣',
                description=
                f"""**📝 ʜᴜ̛ᴏ̛́ɴɢ ᴅᴀ̂̃ɴ**
ɴʜᴀ̂́ɴ ᴠᴀ̀ᴏ ɴᴜ́ᴛ ʙᴇ̂ɴ ᴅᴜ̛ᴏ̛́ɪ ᴅᴇ̂̉ ᴄʜᴏ̣ɴ ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣ ᴠᴀ̀ ʟᴀ̀ᴍ ᴛʜᴇᴏ ʜᴜ̛ᴏ̛́ɴɢ ᴅᴀ̂̃ɴ.

**📌 ʟᴜ̛ᴜ ʏ́**
ᴄʜɪ̉ ʟᴀ̀ᴍ ᴄᴀ́ᴄ ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣ ᴄʜᴜ̛ᴀ ʜᴏᴀ̀ɴ ᴛʜᴀ̀ɴʜ ᴅᴇ̂̉ ɴʜᴀ̣̂ɴ ᴅɪᴇ̂̉ᴍ.

**⚠️ ɴᴏᴛᴇ**
* Nếu là lần đầu trong ngày thì bạn phải vượt LinkNgon trước để mở vượt TrafficUser và Yeumoney *

**💰 ᴅɪᴇ̂̉ᴍ ʜɪᴇ̣̂ɴ ᴛᴀ̣ɪ**
`{totalPoints} ᴅɪᴇ̂̉ᴍ`

`{nowStr}`"""
                , color=discord.Color.blue())
            embed.set_thumbnail(url='http://ltruowng.space/king.png')
            await interaction.response.send_message(embed=embed, view=
                TaskView(self.bot, str(interaction.user.id)))



        @self.bot.tree.command(name='addctv', description='Thêm CTV (Admin only)')
        @app_commands.describe(user='User để thêm làm CTV')
        async def addCTVCommand(interaction: discord.Interaction, user: discord.Member):
            if not Utils.isAdmin(str(interaction.user.id)) and str(interaction.user.id) not in ADMIN_ID:
                await interaction.response.send_message(
                    '❌ Bạn không có quyền sử dụng lệnh này!', ephemeral=True)
                return
            try:
                await interaction.response.defer(ephemeral=True)
                userId = str(user.id)
                Utils.addCTV(userId)
                await interaction.followup.send(
                    f'✅ Đã thêm {user.mention} vào danh sách CTV!\n💡 CTV có thể add tối đa 10 points/người/ngày'
                    , ephemeral=True)
            except Exception as e:
                await interaction.followup.send(f'❌ Lỗi: {str(e)}', ephemeral=True)

        @self.bot.tree.command(name='removectv', description='Xóa CTV (Admin only)')
        @app_commands.describe(user='User để xóa khỏi CTV')
        async def removeCTVCommand(interaction: discord.Interaction, user: discord.Member):
            if not Utils.isAdmin(str(interaction.user.id)) and str(interaction.user.id) not in ADMIN_ID:
                await interaction.response.send_message(
                    '❌ Bạn không có quyền sử dụng lệnh này!', ephemeral=True)
                return
            try:
                await interaction.response.defer(ephemeral=True)
                userId = str(user.id)
                Utils.removeCTV(userId)
                await interaction.followup.send(
                    f'✅ Đã xóa {user.mention} khỏi danh sách CTV!'
                    , ephemeral=True)
            except Exception as e:
                await interaction.followup.send(f'❌ Lỗi: {str(e)}', ephemeral=True)

        @self.bot.tree.command(name='ctvaddpoints', description='CTV add points cho user (CTV only)')
        @app_commands.describe(user='User để add points', points='Số points cần add (tối đa 10/người/ngày)')
        async def ctvAddPointsCommand(interaction: discord.Interaction, user: discord.Member, points: float):
            ctvUserId = str(interaction.user.id)
            if not Utils.isCTV(ctvUserId) and not Utils.isAdmin(ctvUserId) and ctvUserId not in ADMIN_ID:
                await interaction.response.send_message(
                    '❌ Bạn không có quyền CTV để sử dụng lệnh này!', ephemeral=True)
                return

            if points <= 0 or points > 10:
                await interaction.response.send_message(
                    '❌ Số points phải từ 0.1 đến 10!', ephemeral=True)
                return

            try:
                await interaction.response.defer(ephemeral=True)
                targetUserId = str(user.id)
                success, message = Utils.addPointsByCTV(ctvUserId, targetUserId, points)

                if success:
                    await interaction.followup.send(
                        f'✅ {message}\n👤 Target: {user.mention}\n💰 Points: +{points}'
                        , ephemeral=True)
                else:
                    await interaction.followup.send(f'❌ {message}', ephemeral=True)
            except Exception as e:
                await interaction.followup.send(f'❌ Lỗi: {str(e)}', ephemeral=True)

        @self.bot.tree.command(name='setresetcoin', description='Set lịch reset coin tự động (Admin only)')
        @app_commands.describe(
            enabled='Bật/tắt reset tự động',
            day='Ngày trong tuần để reset (0=Chủ nhật, 1=Thứ 2, ..., 6=Thứ 7)'
        )
        @app_commands.choices(day=[
            app_commands.Choice(name='Chủ nhật', value=0),
            app_commands.Choice(name='Thứ 2', value=1),
            app_commands.Choice(name='Thứ 3', value=2),
            app_commands.Choice(name='Thứ 4', value=3),
            app_commands.Choice(name='Thứ 5', value=4),
            app_commands.Choice(name='Thứ 6', value=5),
            app_commands.Choice(name='Thứ 7', value=6)
        ])
        async def setResetCoinCommand(interaction: discord.Interaction, enabled: bool, day: app_commands.Choice[int] = None):
            if not Utils.isAdmin(str(interaction.user.id)) and str(interaction.user.id) not in ADMIN_ID:
                await interaction.response.send_message(
                    '❌ Bạn không có quyền sử dụng lệnh này!', ephemeral=True)
                return
            try:
                await interaction.response.defer(ephemeral=True)

                if enabled and day is None:
                    await interaction.followup.send(
                        '❌ Khi bật reset tự động, bạn phải chọn ngày trong tuần!', ephemeral=True)
                    return

                dayOfWeek = day.value if day else None
                Utils.setResetCoinSchedule(enabled, dayOfWeek)

                if enabled:
                    dayName = day.name
                    await interaction.followup.send(
                        f'✅ Đã bật reset coin tự động vào **{dayName}** hàng tuần!', ephemeral=True)
                else:
                    await interaction.followup.send(
                        f'✅ Đã tắt reset coin tự động!', ephemeral=True)
            except Exception as e:
                await interaction.followup.send(f'❌ Lỗi: {str(e)}', ephemeral=True)

        @self.bot.tree.command(name='resetallcoins', description='Reset tất cả coin của toàn bộ user (Admin only)')
        async def resetAllCoinsCommand(interaction: discord.Interaction):
            if not Utils.isAdmin(str(interaction.user.id)) and str(interaction.user.id) not in ADMIN_ID:
                await interaction.response.send_message(
                    '❌ Bạn không có quyền sử dụng lệnh này!', ephemeral=True)
                return
            try:
                await interaction.response.defer(ephemeral=True)
                totalReset = Utils.resetAllCoins()
                await interaction.followup.send(
                    f'✅ Đã reset tất cả coin cho **{totalReset} user**!', ephemeral=True)
            except Exception as e:
                await interaction.followup.send(f'❌ Lỗi: {str(e)}', ephemeral=True)

        @self.bot.tree.command(name='addpoints', description=
            'Thêm điểm cho user (Admin only)')
        @app_commands.describe(user=
            'User để thêm điểm (có thể dùng ID hoặc mention)', points=
            'Số điểm cần thêm')
        async def addPointsCommand(interaction: discord.Interaction, user:
            str, points: float):
            if not Utils.isAdmin(str(interaction.user.id)) and str(interaction
                .user.id) not in ADMIN_ID:
                try:
                    await interaction.response.send_message(
                        'Bạn không có quyền sử dụng lệnh này!', ephemeral=True)
                except:
                    pass
                return
            if Utils.isChannelLocked(interaction.channel.id):
                embed = discord.Embed(title='🔒 Channel Bị Khóa',
                    description=
                    'Bot không hoạt động trên channel này. Vui lòng liên hệ admin để mở khóa.'
                    , color=discord.Color.red())
                await interaction.response.send_message(embed=embed,
                    ephemeral=True)
                return
            if points <= 0:
                try:
                    await interaction.response.send_message(
                        'Số điểm phải lớn hơn 0!', ephemeral=True)
                except:
                    pass
                return
            try:
                await interaction.response.defer(ephemeral=True)
                userId = None
                userDisplayName = 'Unknown User'
                cleanUser = user.strip()
                if cleanUser.startswith('<@') and cleanUser.endswith('>'):
                    cleanUser = cleanUser[2:-1]
                    if cleanUser.startswith('!'):
                        cleanUser = cleanUser[1:]
                try:
                    userId = str(int(cleanUser))
                    try:
                        discordUser = await interaction.client.fetch_user(int
                            (userId))
                        userDisplayName = (discordUser.display_name or
                            discordUser.name)
                    except:
                        userDisplayName = f'User {userId[:8]}'
                except ValueError:
                    await interaction.followup.send(
                        '❌ ID người dùng không hợp lệ! Vui lòng nhập ID hoặc mention user.'
                        , ephemeral=True)
                    return
                Utils.getOrCreateUser(userId)
                Utils.addPoints(userId, points)
                userData = Utils.getOrCreateUser(userId)
                currentPoints = userData['YEUMONEY_POINTS']
                totalPoints = userData['TOTAL_POINTS']
                embed = discord.Embed(title='✅ Thêm điểm thành công',
                    description=
                    f"""**👤 User:** {userDisplayName} (`{userId}`)
**➕ Điểm thêm:** {points} điểm
**💰 Điểm hiện tại:** {currentPoints} điểm
**💎 Tổng điểm:** {totalPoints} điểm"""
                    , color=discord.Color.green())
                embed.set_thumbnail(url='http://ltruowng.space/king.png')
                await interaction.followup.send(embed=embed, ephemeral=True)
            except Exception as e:
                try:
                    await interaction.followup.send(f'Lỗi khi thêm điểm: {e}',
                        ephemeral=True)
                except:
                    pass

        @self.bot.tree.command(name='aad', description='Thêm admin bot')
        async def aadCommand(interaction: discord.Interaction, user:
            discord.Member):
            if str(interaction.user.id) not in ADMIN_ID:
                try:
                    await interaction.response.send_message(
                        'Bạn không có quyền sử dụng lệnh này!', ephemeral=True)
                except:
                    pass
                return
            if Utils.isChannelLocked(interaction.channel.id):
                embed = discord.Embed(title='🔒 Channel Bị Khóa',
                    description=
                    'Bot không hoạt động trên channel này. Vui lòng liên hệ admin để mở khóa.'
                    , color=discord.Color.red())
                await interaction.response.send_message(embed=embed,
                    ephemeral=True)
                return
            try:
                await interaction.response.defer(ephemeral=True)
                userId = str(user.id)
                Utils.addAdmin(userId)
                await interaction.followup.send(
                    f'Đã thêm {user.mention} làm admin bot!', ephemeral=True)
            except Exception as e:
                try:
                    await interaction.followup.send(f'Lỗi khi thêm admin: {e}',
                        ephemeral=True)
                except:
                    pass

        @self.bot.tree.command(name='sync', description=
            'Sync lại slash commands (admin only)')
        async def syncCommands(interaction: discord.Interaction):
            if not Utils.isAdmin(str(interaction.user.id)) and str(interaction
                .user.id) not in ADMIN_ID:
                return await interaction.response.send_message(
                    'Không có quyền!', ephemeral=True)
            try:
                await interaction.response.defer(ephemeral=True)
                if interaction.guild:
                    synced = await self.bot.tree.sync(guild=interaction.guild)
                    await interaction.followup.send(
                        f'Đã sync {len(synced)} lệnh cho server này! ({interaction.guild.name})'
                        , ephemeral=True)
                else:
                    synced = await self.bot.tree.sync()
                    await interaction.followup.send(
                        f'Đã sync {len(synced)} lệnh toàn cục! (Có thể mất đến 1 giờ để cập nhật)'
                        , ephemeral=True)
            except Exception as e:
                await interaction.followup.send(f'Lỗi khi sync: {str(e)}',
                    ephemeral=True)

        @self.bot.tree.command(name='account', description=
            'Xem số lượng tài khoản còn lại')
        async def accountCommand(interaction: discord.Interaction):
            try:
                await interaction.response.defer()
                import requests
                apiUrl = 'https://shop.kingdev.sbs/api/InfoResource.php'
                params = {'username': 'trz.developer', 'password':
                    'QuanNhjCaBuCuAnhTruong', 'id': '19'}
                response = requests.get(apiUrl, params=params)
                if response.status_code != 200:
                    await interaction.followup.send(
                        f'❌ Lỗi kết nối API: HTTP {response.status_code}',
                        ephemeral=True)
                    return
                try:
                    result = response.json()
                except:
                    await interaction.followup.send(
                        '❌ Lỗi: Không thể parse response từ API', ephemeral
                        =True)
                    return
                print(f'API Response: {result}')
                if result.get('status') == 'success':
                    data = result.get('data', {})
                    if data:
                        if isinstance(data, list) and len(data) > 0:
                            product = data[0]
                        elif isinstance(data, dict):
                            product = data
                        else:
                            await interaction.followup.send(
                                '❌ Dữ liệu sản phẩm không hợp lệ!',
                                ephemeral=True)
                            return
                        name = product.get('name', 'Không xác định')
                        amount = product.get('amount', 0)
                        price = product.get('price', '0')
                        country = product.get('country', 'vn').upper()
                        embed = discord.Embed(title='📊 Thông Tin Tài Khoản',
                            description=
                            f'**Tài Khoản Còn lại:** `{amount}`', color=
                            discord.Color.blue())
                        embed.add_field(name='📝 Tên sản phẩm', value=
                            f'`{name}`', inline=True)
                        embed.add_field(name='💰 Giá', value=f'`{price}`',
                            inline=True)
                        embed.add_field(name='🌍 Quốc gia', value=
                            f'`{country}`', inline=True)
                        embed.set_thumbnail(url=
                            'http://ltruowng.space/king.png')
                        embed.set_footer(text=
                            f'Yêu cầu bởi {interaction.user.display_name}')
                        await interaction.followup.send(embed=embed)
                    else:
                        await interaction.followup.send(
                            '❌ Không tìm thấy dữ liệu sản phẩm trong response!'
                            , ephemeral=True)
                else:
                    errorMsg = result.get('msg', 'Lỗi không xác định')
                    await interaction.followup.send(f'❌ Lỗi API: {errorMsg}',
                        ephemeral=True)
            except Exception as e:
                print(f'Account command error: {e}')
                await interaction.followup.send(f'❌ Lỗi: {str(e)}',
                    ephemeral=True)

        @self.bot.tree.command(name='lock', description=
            'Khóa channel - Bot sẽ không hoạt động trên channel này')
        async def lockCommand(interaction: discord.Interaction):
            if not Utils.isAdmin(str(interaction.user.id)) and str(interaction
                .user.id) not in ADMIN_ID:
                await interaction.response.send_message(
                    '❌ Bạn không có quyền sử dụng lệnh này!', ephemeral=True)
                return
            try:
                channelId = interaction.channel.id
                if Utils.isChannelLocked(channelId):
                    await interaction.response.send_message(
                        '❌ Channel này đã bị khóa rồi!', ephemeral=True)
                    return
                Utils.lockChannel(channelId)
                embed = discord.Embed(title='🔒 Channel Đã Bị Khóa',
                    description=
                    f"""**Channel:** {interaction.channel.mention}
**Khóa bởi:** {interaction.user.mention}
**Thời gian:** {Utils.getFormattedDateTime()}"""
                    , color=discord.Color.red())
                embed.set_thumbnail(url='http://ltruowng.space/king.png')
                await interaction.response.send_message(embed=embed)
            except Exception as e:
                await interaction.response.send_message(
                    f'❌ Lỗi khi khóa channel: {str(e)}', ephemeral=True)

        @self.bot.tree.command(name='unlock', description=
            'Mở khóa channel - Bot sẽ hoạt động bình thường')
        async def unlockCommand(interaction: discord.Interaction):
            if not Utils.isAdmin(str(interaction.user.id)) and str(interaction
                .user.id) not in ADMIN_ID:
                await interaction.response.send_message(
                    '❌ Bạn không có quyền sử dụng lệnh này!', ephemeral=True)
                return
            try:
                channelId = interaction.channel.id
                if not Utils.isChannelLocked(channelId):
                    await interaction.response.send_message(
                        '❌ Channel này chưa bị khóa!', ephemeral=True)
                    return
                Utils.unlockChannel(channelId)
                embed = discord.Embed(title='🔓 Channel Đã Được Mở Khóa',
                    description=
                    f"""**Channel:** {interaction.channel.mention}
**Mở khóa bởi:** {interaction.user.mention}
**Thời gian:** {Utils.getFormattedDateTime()}"""
                    , color=discord.Color.green())
                embed.set_thumbnail(url='http://ltruowng.space/king.png')
                await interaction.response.send_message(embed=embed)
            except Exception as e:
                await interaction.response.send_message(
                    f'❌ Lỗi khi mở khóa channel: {str(e)}', ephemeral=True)

        @self.bot.tree.command(name='reset', description=
            'Reset thủ công lại giới hạn nhiệm vụ ngày (admin only)')
        async def resetCommand(interaction: discord.Interaction):
            if not Utils.isAdmin(str(interaction.user.id)) and str(interaction
                .user.id) not in ADMIN_ID:
                await interaction.response.send_message(
                    '❌ Bạn không có quyền sử dụng lệnh này!', ephemeral=True)
                return
            try:
                await interaction.response.defer(ephemeral=True)
                from database import db
                db.executeQuery('TRUNCATE TABLE YEUMONEY_PROGRESS')
                db.executeQuery('TRUNCATE TABLE TASK_LINKS')
                db.executeQuery('TRUNCATE TABLE DAILY_TASKS')
                db.executeQuery(
                    'UPDATE USERS_DATA SET YEUMONEY_DAILY_COUNT = 0')
                await interaction.followup.send(
                    '✅ Đã reset lại giới hạn nhiệm vụ ngày cho toàn bộ user!',
                    ephemeral=True)
            except Exception as e:
                await interaction.followup.send(f'❌ Lỗi khi reset: {e}',
                    ephemeral=True)

    def run(self):
        self.bot.run(
            'MTMwOTIxNDQzMzc3OTY0NjYzMg.G_UFoe._B_ye82iKXECjIkeAdP-_xfGkuj-IwK3a0mjRQ'
            )

    async def dmQueueWorker(self):
        while True:
            try:
                queue = Utils.getDmQueue()
                for item in queue:
                    userId = item['USER_ID']
                    content = item['MESSAGE_CONTENT']
                    filePath = item['FILE_PATH']
                    try:
                        user = await self.bot.fetch_user(int(userId))
                        if filePath:
                            if os.path.basename(filePath).startswith(
                                'King_Dev_') and not os.path.dirname(filePath):
                                filePath = os.path.join('tmp', filePath)
                            await user.send(content, file=discord.File(
                                filePath))
                        else:
                            await user.send(content)
                        print(f'[DM QUEUE] Đã gửi DM cho {userId}')
                        Utils.markDmProcessed(item['QUEUE_ID'])
                    except Exception as e:
                        print(f'[DM QUEUE] Lỗi gửi DM cho {userId}: {e}')
            except Exception as e:
                print(f'[DM QUEUE] Lỗi worker: {e}')
            await asyncio.sleep(5)

    async def dailyResetWorker(self):
        import datetime, asyncio
        while True:
            now = datetime.datetime.now()
            next_run = (now + datetime.timedelta(days=1)).replace(hour=0,
                minute=0, second=0, microsecond=0)
            wait_seconds = (next_run - now).total_seconds()
            print(
                f'[DAILY RESET] Sẽ reset nhiệm vụ sau {wait_seconds:.0f} giây')
            await asyncio.sleep(wait_seconds)
            from utils import Utils
            Utils.resetYeumoneyDailyProgress()
            print('[DAILY RESET] Đã reset nhiệm vụ cho toàn bộ user!')

            # Kiểm tra reset coin schedule
            await self.checkResetCoinSchedule()

    async def checkResetCoinSchedule(self):
        """Kiểm tra và thực hiện reset coin theo lịch"""
        import datetime
        from utils import Utils

        schedule = Utils.getResetCoinSchedule()
        if not schedule['enabled']:
            return

        now = datetime.datetime.now()
        current_day_of_week = now.weekday() + 1  # Python: 0=Monday, chuyển thành 1=Monday
        if current_day_of_week == 7:  # Sunday
            current_day_of_week = 0

        if current_day_of_week == schedule['day_of_week']:
            totalReset = Utils.resetAllCoins()
            print(f'[RESET COIN] Đã reset coin cho {totalReset} user vào {schedule["day_name"]}!')

            # Gửi thông báo đến channel admin nếu có
            try:
                # Có thể thêm logic gửi thông báo đến Discord channel ở đây
                pass
            except Exception as e:
                print(f'[RESET COIN] Lỗi gửi thông báo: {e}')


class TaskView(View):
    def __init__(self, bot, userId=None):
        super().__init__(timeout=None)
        self.bot = bot
        self.userId = userId
        if userId:
            # Kiểm tra xem user đã hoàn thành LinkNgon hôm nay chưa
            linkngonCompleted = Utils.isLinkngonUsedToday(userId)

            # LinkNgon button - luôn hiển thị đầu tiên nếu chưa hoàn thành
            if not linkngonCompleted:
                linkngonButton = Button(label='[ LinkNgon (1/1 /ngày) ]', style=discord.ButtonStyle.gray, custom_id='task_linkngon')
                linkngonButton.callback = self.makeCallback('[ LinkNgon ]')
                self.add_item(linkngonButton)

            # TrafficUser button - chỉ hiển thị nếu đã hoàn thành LinkNgon
            if linkngonCompleted and not Utils.isTrafficuserUsedToday(userId):
                trafficuserButton = Button(label='[ TrafficUser (1/1 /ngày) ]', style=discord.ButtonStyle.gray, custom_id='task_trafficuser')
                trafficuserButton.callback = self.makeCallback('[ TrafficUser ]')
                self.add_item(trafficuserButton)

            # Yeumoney button - chỉ hiển thị nếu đã hoàn thành LinkNgon
            if linkngonCompleted:
                progressData = Utils.getYeumoneyProgressData(userId)
                current_progress = progressData['PROGRESS_LEVEL'] if progressData else 0
                maxDaily = 3
                isLocked = Utils.isYeumoneyLocked(userId, maxDaily)
                if not isLocked and current_progress < maxDaily:
                    yeumoneyLabel = f'[ ʏᴇᴜᴍᴏɴᴇʏ ( {current_progress}/3 /ngày ) ]'
                    yeumoneyButton = Button(label=yeumoneyLabel, style=discord.ButtonStyle.gray, custom_id='task_yeumoney')
                    yeumoneyButton.callback = self.makeCallback('[ ʏᴇᴜᴍᴏɴᴇʏ ]')
                    self.add_item(yeumoneyButton)

        if userId:
            totalPoints = Utils.getTotalPoints(userId)
            exchangeButton = Button(
                label='[ ĐỔI ĐIỂM ]',
                style=discord.ButtonStyle.green if totalPoints >= 0.75 else discord.ButtonStyle.gray,
                custom_id='task_exchange',
                disabled=totalPoints < 0.75
            )
            exchangeButton.callback = self.makeCallback('[ ĐỔI ĐIỂM ]')
            self.add_item(exchangeButton)

    def makeCallback(self, task):

        async def callback(interaction: discord.Interaction):
            if str(interaction.user.id) != self.userId:
                await interaction.response.send_message(
                    'Bạn không thể sử dụng menu này!', ephemeral=True)
                return
            userId = str(interaction.user.id)
            user = interaction.user
            userInfo = {'username': user.name, 'displayName': user.
                display_name or user.name, 'avatar': str(user.avatar.url) if
                user.avatar else None}
            Utils.updateUserInfo(userId, userInfo)
            if task == '[ ʏᴇᴜᴍᴏɴᴇʏ ]':
                # Kiểm tra xem đã hoàn thành LinkNgon chưa
                if not Utils.isLinkngonUsedToday(userId):
                    await interaction.response.send_message(
                        '⚠️ Bạn phải hoàn thành nhiệm vụ LinkNgon trước để mở khóa Yeumoney!'
                        , ephemeral=True)
                    return

                progressData = Utils.getYeumoneyProgressData(userId)
                currentProgress = progressData['PROGRESS_LEVEL'
                    ] if progressData else 0
                maxDaily = 3
                isLocked = Utils.isYeumoneyLocked(userId, maxDaily)
                if isLocked or currentProgress >= maxDaily:
                    Utils.saveYeumoneyProgress(userId, maxDaily, '')
                    await interaction.response.send_message(
                        f'🎉 Bạn đã hoàn thành đủ {maxDaily}/{maxDaily} nhiệm vụ hôm nay! Vui lòng chờ reset vào 0:00 ngày mai.'
                        , ephemeral=True)
                    return
                token = progressData['REDIRECT_TOKEN'
                    ] if progressData else None
                if token and not Utils.isTokenUsed(token):
                    link = (
                        f'https://dashboard.kingdev.sbs/api/yeumoney?token={token}'
                        )
                    await interaction.response.send_message(
                        f"""• Bạn đã tạo link nhiệm vụ này rồi!
• Link: {link}
• Progress: ({currentProgress}/{maxDaily})"""
                        , ephemeral=True)
                    return
                progressData = Utils.getYeumoneyProgressData(userId)
                currentProgress = progressData['PROGRESS_LEVEL'
                    ] if progressData else 0
                encrypted = CryptoUtils.encrypt(userId)
                jwtToken = CryptoUtils.createJwt(encrypted)
                link = (
                    f'https://dashboard.kingdev.sbs/api/yeumoney?token={jwtToken}'
                    )
                Utils.saveTaskLink(userId, 'yeumoney', link, currentProgress)
                Utils.saveYeumoneyProgress(userId, currentProgress, jwtToken)
                await interaction.response.send_message(
                    f"""• ʙᴀ̣ɴ ᴅᴀ̃ ᴄʜᴏ̣ɴ ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣ **{task}** ({currentProgress}/{maxDaily} hôm nay).
• ɴʜᴀ̂́ɴ ᴠᴀ̀ᴏ ʟɪɴᴋ ꜱᴀᴜ ᴅᴇ̂̉ ᴛɪᴇ̂́ᴘ ᴛᴜ̣ᴄ: {link}"""
                    , ephemeral=True)
            elif task == '[ LinkNgon ]':
                if Utils.isLinkngonUsedToday(userId):
                    await interaction.response.send_message(
                        f'• Bạn đã hoàn thành nhiệm vụ LinkNgon hôm nay rồi!'
                        , ephemeral=True)
                    return
                existingLink = Utils.getTaskLink(userId, 'linkngon', 0)
                if existingLink:
                    await interaction.response.send_message(
                        f"""• Bạn đã tạo link nhiệm vụ này hôm nay rồi!
• Link: {existingLink}"""
                        , ephemeral=True)
                    return
                encrypted = CryptoUtils.encrypt(userId)
                jwtToken = CryptoUtils.createJwt(encrypted)
                link = (
                    f'https://dashboard.kingdev.sbs/api/linkngon?token={jwtToken}'
                    )
                Utils.saveTaskLink(userId, 'linkngon', link, 0)
                await interaction.response.send_message(
                    f"""• ʙᴀ̣ɴ ᴅᴀ̃ ᴄʜᴏ̣ɴ ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣ **{task}** (1/1 hôm nay).
• ɴʜᴀ̂́ɴ ᴠᴀ̀ᴏ ʟɪɴᴋ ꜱᴀᴜ ᴅᴇ̂̉ ᴛɪᴇ̂́ᴘ ᴛᴜ̣ᴄ: {link}"""
                    , ephemeral=True)
            elif task == '[ TrafficUser ]':
                # Kiểm tra xem đã hoàn thành LinkNgon chưa
                if not Utils.isLinkngonUsedToday(userId):
                    await interaction.response.send_message(
                        '⚠️ Bạn phải hoàn thành nhiệm vụ LinkNgon trước để mở khóa TrafficUser!'
                        , ephemeral=True)
                    return

                # Kiểm tra xem đã hoàn thành task TrafficUser hôm nay chưa
                if Utils.isTrafficuserUsedToday(userId):
                    await interaction.response.send_message(
                        f'• Bạn đã hoàn thành nhiệm vụ TrafficUser hôm nay rồi!'
                        , ephemeral=True)
                    return
                # Kiểm tra xem đã có link TrafficUser hôm nay chưa
                existingLink = Utils.getTaskLink(userId, 'trafficuser', 0)
                if existingLink:
                    await interaction.response.send_message(
                        f"""• Bạn đã tạo link nhiệm vụ này hôm nay rồi!
• Link: {existingLink}"""
                        , ephemeral=True)
                    return
                encrypted = CryptoUtils.encrypt(userId)
                jwtToken = CryptoUtils.createJwt(encrypted)
                link = (
                    f'https://dashboard.kingdev.sbs/api/trafficuser?token={jwtToken}'
                    )
                Utils.saveTaskLink(userId, 'trafficuser', link, 0)
                await interaction.response.send_message(
                    f"""• ʙᴀ̣ɴ ᴅᴀ̃ ᴄʜᴏ̣ɴ ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣ **{task}** (1/1 hôm nay).
• ɴʜᴀ̂́ɴ ᴠᴀ̀ᴏ ʟɪɴᴋ ꜱᴀᴜ ᴅᴇ̂̉ ᴛɪᴇ̂́ᴘ ᴛᴜ̣ᴄ: {link}"""
                    , ephemeral=True)
            elif task == '[ ĐỔI ĐIỂM ]':
                totalPoints = Utils.getTotalPoints(userId)
                if totalPoints < 0.75:
                    embed = discord.Embed(title='💰 Menu Đổi Điểm',
                        description=
                        f"""**💎 Đɪểᴍ ʜɪệɴ ᴛạɪ: `{totalPoints} đɪểᴍ`**

**❌ Không đủ điểm để đổi!**
Bạn cần ít nhất **0.75 điểm** để đổi email.

**📋 ʙảɴɢ ɢɪá đổɪ đɪểᴍ:**
• **Ugphone:** 1 điểm = 1 tài khoản
• **Redfinger:** 3 điểm = 1 tài khoản
• **Email:** 0.75 điểm = 3 email

**💡 ʟᴏ̛ɪ ᴋʜᴜʏᴇ̂ɴ:**
Hãy làm các nhiệm vụ để kiếm thêm điểm!"""
                        , color=discord.Color.red())
                    embed.set_thumbnail(url='http://ltruowng.space/king.png')
                    await interaction.response.send_message(embed=embed,
                        ephemeral=True)
                    return
                embed = discord.Embed(title='💰 ᴍᴇɴᴜ ĐỔɪ ĐɪỂᴍ', description=
                    f"""**💎 Đɪểᴍ ʜɪệɴ ᴛạɪ: `{totalPoints} đɪểᴍ`**

**📋 ʙảɴɢ ɢɪá đổɪ đɪểᴍ:**
• **Ugphone:** 1 điểm = 1 tài khoản
• **Redfinger:** 3 điểm = 1 tài khoản
• **Email:** 0.75 điểm = 3 email

**⚠️ ʟưᴜ ý:**
• Tài khoản sẽ được gửi qua DM
• Không thể hoàn lại đɪểᴍ sau khi đổɪ

**🎯 Chọn loại tài khoản muốn đổi:**"""
                    , color=discord.Color.gold())
                embed.set_thumbnail(url='http://ltruowng.space/king.png')
                exchangeView = ExchangeMainView(self.bot, userId)
                await interaction.response.send_message(embed=embed, view=
                    exchangeView, ephemeral=True)
            else:
                await interaction.response.send_message(
                    f"""• ʙᴀ̣ɴ ᴅᴀ̃ ᴄʜᴏ̣ɴ ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣ **{task}**.
• ɴʜɪᴇ̣̂ᴍ ᴠᴜ̣ ɴᴀ̀ʏ ᴄʜᴜ̛ᴀ ᴄᴏ́ ꜱᴀ̆̃ɴ."""
                    , ephemeral=True)
        return callback


class ExchangeMainView(View):

    def __init__(self, bot, userId):
        super().__init__(timeout=None)
        self.bot = bot
        self.userId = userId
        totalPoints = Utils.getTotalPoints(userId)
        emailButton = Button(label='📧 Email', style=discord.ButtonStyle.
            secondary, custom_id='exchange_main_email', disabled=
            totalPoints < 0.75)
        emailButton.callback = self.makeMainCallback('email')
        self.add_item(emailButton)
        ugphoneButton = Button(label='📱 Ugphone', style=discord.ButtonStyle
            .primary, custom_id='exchange_main_ugphone', disabled=
            totalPoints < 1)
        ugphoneButton.callback = self.makeMainCallback('ugphone')
        self.add_item(ugphoneButton)
        redfingerButton = Button(label='🖥️ Redfinger', style=discord.
            ButtonStyle.success, custom_id='exchange_main_redfinger',
            disabled=totalPoints < 3)
        redfingerButton.callback = self.makeMainCallback('redfinger')
        self.add_item(redfingerButton)

    def makeMainCallback(self, exchangeType):

        async def callback(interaction: discord.Interaction):
            if str(interaction.user.id) != self.userId:
                await interaction.response.send_message(
                    'Bạn không thể sử dụng menu này!', ephemeral=True)
                return
            totalPoints = Utils.getTotalPoints(self.userId)
            if exchangeType == 'email':
                embed = discord.Embed(title='📧 ĐỔI EMAIL', description=
                    f"""**💎 Điểm hiện tại: `{totalPoints} điểm`**

**📋 Bảng giá Email:**
• **0.75 điểm** = 3 email
• **1.5 điểm** = 6 email
• **2.25 điểm** = 9 email
• **3.0 điểm** = 12 email

**⚠️ Lưu ý:**
• Email sẽ được gửi qua DM
• Không thể hoàn lại điểm sau khi đổi"""
                    , color=discord.Color.blue())
            elif exchangeType == 'ugphone':
                embed = discord.Embed(title='📱 ĐỔI UGPHONE', description=
                    f"""**💎 Điểm hiện tại: `{totalPoints} điểm`**

**📋 Bảng giá Ugphone:**
• **1 điểm** = 1 tài khoản
• **2 điểm** = 2 tài khoản
• **3 điểm** = 3 tài khoản
• **4 điểm** = 4 tài khoản
• **5 điểm** = 5 tài khoản

**⚠️ Lưu ý:**
• Tài khoản sẽ được gửi qua DM
• Không thể hoàn lại điểm sau khi đổi"""
                    , color=discord.Color.blue())
            else:
                embed = discord.Embed(title='🖥️ ĐỔI REDFINGER', description
                    =
                    f"""**💎 Điểm hiện tại: `{totalPoints} điểm`**

**📋 Bảng giá Redfinger:**
• **3 điểm** = 1 tài khoản
• **6 điểm** = 2 tài khoản
• **9 điểm** = 3 tài khoản
• **12 điểm** = 4 tài khoản
• **15 điểm** = 5 tài khoản

**⚠️ Lưu ý:**
• Tài khoản sẽ được gửi qua DM
• Không thể hoàn lại điểm sau khi đổi
• APP LOG REDFINGER : ```https://www.mediafire.com/file/h8tibl096m04hxj/bluefinger.apk/file```"""
                    , color=discord.Color.green())
            embed.set_thumbnail(url='http://ltruowng.space/king.png')
            exchangeView = ExchangeDetailView(self.bot, self.userId,
                exchangeType)
            await interaction.response.edit_message(embed=embed, view=
                exchangeView)
        return callback


class ExchangeDetailView(View):

    def __init__(self, bot, userId, exchangeType):
        super().__init__(timeout=None)
        self.bot = bot
        self.userId = userId
        self.exchangeType = exchangeType
        totalPoints = Utils.getTotalPoints(userId)
        if exchangeType == 'email':
            options = [0.75, 1.5, 2.25, 3.0]
            for points in options:
                if totalPoints >= points:
                    emails = int(points / 0.75 * 3)
                    button = Button(label=f'{points} điểm = {emails} email',
                        style=discord.ButtonStyle.secondary, custom_id=
                        f'exchange_email_{points}')
                    button.callback = self.makeExchangeCallback(points)
                    self.add_item(button)
        elif exchangeType == 'ugphone':
            options = [1, 2, 3, 4, 5]
            for points in options:
                if totalPoints >= points:
                    button = Button(label=f'{points} điểm = {points} TK',
                        style=discord.ButtonStyle.primary, custom_id=
                        f'exchange_ugphone_{points}')
                    button.callback = self.makeExchangeCallback(points)
                    self.add_item(button)
        elif exchangeType == 'redfinger':
            options = [3, 6, 9, 12, 15]
            for points in options:
                if totalPoints >= points:
                    accounts = points // 3
                    button = Button(label=f'{points} điểm = {accounts} TK',
                        style=discord.ButtonStyle.success, custom_id=
                        f'exchange_redfinger_{points}')
                    button.callback = self.makeExchangeCallback(points)
                    self.add_item(button)
        backButton = Button(label='🔙 Quay lại', style=discord.ButtonStyle.
            gray, custom_id='exchange_back')
        backButton.callback = self.backCallback
        self.add_item(backButton)

    def makeExchangeCallback(self, points):

        async def callback(interaction: discord.Interaction):
            if str(interaction.user.id) != self.userId:
                await interaction.response.send_message(
                    'Bạn không thể sử dụng menu này!', ephemeral=True)
                return
            totalPoints = Utils.getTotalPoints(self.userId)
            if totalPoints < points:
                await interaction.response.send_message(
                    f'❌ Không đủ điểm! Bạn có {totalPoints} điểm, cần {points} điểm để đổi.'
                    , ephemeral=True)
                return
            await interaction.response.defer(ephemeral=True)
            if self.exchangeType == 'ugphone':
                success, message, accounts = Utils.exchangePointsUgphone(self
                    .userId, points)
            elif self.exchangeType == 'redfinger':
                success, message, accounts = Utils.exchangePointsRedfinger(self
                    .userId, points)
            elif self.exchangeType == 'email':
                success, message, accounts = Utils.exchangePointsEmail(self
                    .userId, points)
            else:
                await interaction.followup.send('❌ Loại đổi điểm không hợp lệ!'
                    , ephemeral=True)
                return
            if success:
                try:
                    user = self.bot.get_user(int(self.userId))
                    if not user:
                        user = await self.bot.fetch_user(int(self.userId))
                    if user and accounts:
                        fileName = generateUniqueFileName(self.userId)
                        filePath = os.path.join('tmp', fileName)
                        os.makedirs('tmp', exist_ok=True)
                        if not checkExistingFile(self.userId, filePath):
                            with open(filePath, 'w', encoding='utf-8') as f:
                                f.write(accounts)
                            dmMessage = (
                                f'``` >> Accounts [ {self.exchangeType.upper()} ]'
                                )
                            await user.send(dmMessage, file=discord.File(
                                filePath))
                            try:
                                os.remove(filePath)
                            except:
                                pass
                            print(
                                f'>> [ EXCHANGE ] SENT {self.exchangeType.upper()} ACCOUNTS TO {self.userId} FOR {points} POINTS with file: {fileName}'
                                )
                        else:
                            print(
                                f'>> [ EXCHANGE ] File already exists for user: {self.userId}, skipping...'
                                )
                    await interaction.followup.send(
                        f'✅ {message}\n📨 Tài khoản đã được gửi qua DM!',
                        ephemeral=True)
                except Exception as e:
                    print(f'Error sending DM for exchange: {e}')
                    await interaction.followup.send(
                        f"""✅ {message}
❌ Lỗi gửi DM, vui lòng liên hệ admin!"""
                        , ephemeral=True)
            else:
                await interaction.followup.send(f'❌ {message}', ephemeral=True)
        return callback

    async def backCallback(self, interaction: discord.Interaction):
        if str(interaction.user.id) != self.userId:
            await interaction.response.send_message(
                'Bạn không thể sử dụng menu này!', ephemeral=True)
            return
        totalPoints = Utils.getTotalPoints(self.userId)
        embed = discord.Embed(title='💰 ᴍᴇɴᴜ ĐỔɪ ĐɪỂᴍ', description=
            f"""**💎 Đɪểᴍ ʜɪệɴ ᴛạɪ: `{totalPoints} đɪểᴍ`**

**📋 ʙảɴɢ ɢɪá đổɪ đɪểᴍ:**
• **Ugphone:** 1 điểm = 1 tài khoản
• **Redfinger:** 3 điểm = 1 tài khoản
• **Email:** 0.75 điểm = 3 email

**⚠️ ʟưᴜ ý:**
• Tài khoản sẽ được gửi qua DM
• Không thể hoàn lại đɪểᴍ sau khi đổɪ

**🎯 Chọn loại tài khoản muốn đổi:**"""
            , color=discord.Color.gold())
        embed.set_thumbnail(url='http://ltruowng.space/king.png')
        exchangeView = ExchangeMainView(self.bot, self.userId)
        await interaction.response.edit_message(embed=embed, view=exchangeView)


if __name__ == '__main__':
    discordBot = DiscordBot()
    discordBot.run()
