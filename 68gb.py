import asyncio
import re
import websockets
import json
from datetime import datetime

def decodeDiceResult(message: bytes):
    matches = re.findall(rb"\{(\d+)-(\d+)-(\d+)\}", message)
    results = []
    for match in matches:
        x, y, z = map(int, match)
        total = x + y + z
        if total < 10:
            label = "X"
        elif total > 11:
            label = "T"
        else:
            label = "H"
        results.append((x, y, z, label))
    return results
def encode_varint(value: int) -> bytes:
    # Protobuf varint encoding
    buf = bytearray()
    while value > 0x7F:
        buf.append((value & 0x7F) | 0x80)
        value >>= 7
    buf.append(value)
    return bytes(buf)

def buildLichSuPacket(index: int) -> bytes:
    packet_prefix = bytes.fromhex(
        "04 00 00 29 00 55 24 6D 6E 6D 64 73 62 2E 6D 6E 6D 64 73 62 68 61 6E 64 "
        "6C 65 72 2E 72 65 71 6F 70 65 6E 6C 74 74 79 69 6E 66 6F"
    )
    field_id = 1
    varint_tag = (field_id << 3) | 0
    return packet_prefix + bytes([varint_tag]) + encode_varint(index)
async def ketNoiVaGui():
    uri = "wss://99atkf9bz0ugnu9.cq.qnwxdhwica.com/"
    header = [
        ("User-Agent", "Mozilla/5.0"),
        ("Origin", "https://68gbvn25.biz"),
        ("Host", "99atkf9bz0ugnu9.cq.qnwxdhwica.com"),
        ("Connection", "Upgrade"),
        ("Upgrade", "websocket"),
        ("Sec-WebSocket-Version", "13"),
        ("Sec-WebSocket-Key", "OTzv8CqLk+G21kCRFgP3BA==")
    ]

    max_retries = 3
    retry_delay = 5

    for attempt in range(max_retries):
        try:
            print(f"🔗 Đang kết nối... (lần thử {attempt + 1}/{max_retries})")

            async with websockets.connect(uri, extra_headers=header, ping_interval=30, ping_timeout=10) as ws:
                await ws.send(bytes.fromhex(
                    "01 00 00 72 7B 22 73 79 73 22 3A 7B 22 70 6C 61 74 66 6F 72 6D 22 3A 22 6A "
                    "73 2D 77 65 62 73 6F 63 6B 65 74 22 2C 22 63 6C 69 65 6E 74 42 75 69 6C 64 "
                    "4E 75 6D 62 65 72 22 3A 22 30 2E 30 2E 31 22 2C 22 63 6C 69 65 6E 74 56 65 "
                    "72 73 69 6F 6E 22 3A 22 30 61 32 31 34 38 31 64 37 34 36 66 39 32 66 38 34 "
                    "32 38 65 31 62 36 64 65 65 62 37 36 66 65 61 22 7D 7D"
                ))
                await ws.recv()
                await ws.send(b"\x02\x00\x00\x00")

                await ws.send(bytes.fromhex(
                    "04 00 00 4D 01 01 00 01 08 02 10 CA 01 1A 40 61 35 36 32 36 65 31 30 36 33 "
                    "36 39 34 61 31 31 38 32 32 39 66 35 31 32 62 31 39 36 37 39 33 61 32 34 30 "
                    "61 66 31 62 31 35 32 35 31 34 66 35 30 61 30 38 32 38 34 65 34 33 61 37 61 "
                    "37 34 32 33 42 00"
                ))

                while True:
                    data = await ws.recv()
                    if isinstance(data, bytes) and b"https://68gb" in data:
                        await ws.send(bytes.fromhex(
                            "04 00 00 25 00 2D 22 6D 6E 6D 64 73 62 2E 6D 6E 6D 64 73 62 68 61 6E 64 "
                            "6C 65 72 2E 65 6E 74 65 72 67 61 6D 65 72 6F 6F 6D"
                        ))
                        break

                print("✅ Kết nối thành công! Bắt đầu lấy lịch sử...")

                successful_requests = 0
                all_results = []

                for i in range(1, 21):
                    try:
                        print(f"🔁 Gửi gói lịch sử #{i} (gamePlayNum = {i}) ...")
                        await ws.send(buildLichSuPacket(i))

                        timeout_counter = 0
                        max_timeout = 5
                        received_response = False

                        while timeout_counter < max_timeout:
                            try:
                                res = await asyncio.wait_for(ws.recv(), timeout=5.0)
                                if isinstance(res, bytes) and res.startswith(b"\x04"):
                                    results = decodeDiceResult(res)
                                    if results:
                                        print(f"📦 Kết quả lịch sử (gói #{i}):")
                                        for x, y, z, label in results:
                                            print(f"  {x}-{y}-{z} => {label}")
                                            all_results.append({
                                                "game_num": i,
                                                "dice": [x, y, z],
                                                "total": x + y + z,
                                                "result": label,
                                                "timestamp": datetime.now().isoformat()
                                            })
                                        successful_requests += 1
                                        received_response = True

                                        # Sau gói đầu tiên thành công, gửi gói query jackpot
                                        if i == 1:
                                            await asyncio.sleep(1)  # Đợi 1 giây trước khi gửi
                                            print("🎰 Gửi gói query jackpot...")
                                            try:
                                                await ws.send(bytes.fromhex(
                                                    "04 00 00 22 00 1C 1F 67 61 6D 65 63 65 6E 2E 67 61 6D 65 63 65 6E 74 65 72 2E 71 75 65 72 79 6A 61 63 6B 70 6F 74"
                                                ))
                                                # Đợi phản hồi từ query jackpot
                                                jackpot_res = await asyncio.wait_for(ws.recv(), timeout=3.0)
                                                print("✅ Đã nhận phản hồi query jackpot")
                                            except asyncio.TimeoutError:
                                                print("⏰ Timeout khi đợi phản hồi query jackpot")
                                            except Exception as e:
                                                print(f"❌ Lỗi khi gửi query jackpot: {e}")

                                        break
                                timeout_counter += 1
                            except asyncio.TimeoutError:
                                timeout_counter += 1
                                print(f"⏰ Timeout lần {timeout_counter}/{max_timeout}")
                                continue

                        if not received_response:
                            print(f"❌ Không nhận được phản hồi cho gói #{i}")
                            if successful_requests == 0:
                                print("❌ Không có gói nào thành công, có thể server đang chặn")
                                break
                            elif successful_requests < 3:
                                print("❌ Quá ít gói thành công, dừng để tránh bị chặn")
                                break



                        if i < 20:
                            delay = min(5 + (i * 0.5), 15)
                            print(f"⏳ Đợi {delay:.1f} giây trước gói tiếp theo...")
                            await asyncio.sleep(delay)

                    except websockets.exceptions.ConnectionClosed:
                        print(f"❌ Kết nối bị đóng tại gói #{i}")
                        if successful_requests > 0:
                            print(f"✅ Đã lấy được {successful_requests} gói trước khi bị ngắt kết nối")
                        raise
                    except Exception as e:
                        print(f"❌ Lỗi khi xử lý gói #{i}: {e}")
                        continue

                if all_results:
                    filename = f"68gb_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(all_results, f, ensure_ascii=False, indent=2)
                    print(f"💾 Đã lưu {len(all_results)} kết quả vào file: {filename}")

                print("🎉 Hoàn thành lấy lịch sử!")
                return

        except websockets.exceptions.ConnectionClosed as e:
            print(f"❌ Kết nối WebSocket bị đóng: {e}")
        except websockets.exceptions.WebSocketException as e:
            print(f"❌ Lỗi WebSocket: {e}")
        except Exception as e:
            print(f"❌ Lỗi không xác định: {e}")

        if attempt < max_retries - 1:
            print(f"⏳ Đợi {retry_delay} giây trước khi thử lại...")
            await asyncio.sleep(retry_delay)
            retry_delay *= 2
        else:
            print("❌ Đã thử tối đa số lần, không thể kết nối!")


asyncio.run(ketNoiVaGui())
