import mysql.connector
from mysql.connector import Error
import json
import os
import logging
from datetime import timedelta, timezone

# Thiết lập logger
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# <PERSON><PERSON><PERSON> hình múi giờ và file JSON
VIETNAM_TZ = timezone(timedelta(hours=7))
JSON_FILE = 'user_points.json'

DB_CONFIG = {
    'host': '***********',
    'user': 'ihivpyi_botking',
    'password': 't0]bKUvis9B#',
    'database': 'ihivpyi_bot',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci',
    'autocommit': False,
    'connection_timeout': 10,
    'pool_reset_session': True,
    'pool_size': 5,
    'pool_name': 'kingbot_pool'
}
class DatabaseManager:
    def __init__(self):
        self.connection = None
        self.connect()

    def connect(self):
        try:
            if self.connection:
                try:
                    self.connection.close()
                except:
                    pass
            self.connection = mysql.connector.connect(**DB_CONFIG)
            if self.connection.is_connected():
                logger.info("[ DATABASE ] CONNECT SUCCESS")
                cursor = self.connection.cursor()
                cursor.execute("SET SESSION wait_timeout = 28800")
                cursor.execute("SET SESSION interactive_timeout = 28800")
                cursor.close()
        except Error as e:
            logger.error(f"[ DATABASE ] CONNECT FAILED: {e}")
            raise e

    def ensureConnection(self):
        try:
            if self.connection is None or not self.connection.is_connected():
                self.connect()
        except Exception as e:
            logger.warning(f"Connection check failed: {e}, reconnecting...")
            self.connect()

    def executeQuery(self, query, params=None, fetch=False, retry_count=0):
        max_retries = 3
        try:
            self.ensureConnection()
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, params or ())
            if fetch:
                result = cursor.fetchall()
                return result
            else:
                self.connection.commit()
                return cursor.rowcount
        except Error as e:
            error_msg = str(e)
            logger.error(f"[ ERROR DATABASE ] QUERY: {error_msg}")
            if ("Lost connection" in error_msg or "MySQL server has gone away" in error_msg) and retry_count < max_retries:
                logger.warning(f"[ DATABASE ] Connection lost, retrying... ({retry_count + 1}/{max_retries})")
                try:
                    self.connection.rollback()
                except:
                    pass
                self.connect()
                return self.executeQuery(query, params, fetch, retry_count + 1)
            else:
                try:
                    self.connection.rollback()
                except:
                    pass
                raise e


# ✅ Lưu tất cả điểm user từ database xuống file JSON
def save_user_points_to_json(db: DatabaseManager):
    query = "SELECT USER_ID, USER_NAME, YEUMONEY_POINTS, TOTAL_POINTS FROM USERS_DATA"
    try:
        users = db.executeQuery(query, fetch=True)
        user_data = {
            user['USER_ID']: {
                'user_name': user['USER_NAME'],
                'yeumoney_points': float(user['YEUMONEY_POINTS']),
                'total_points': float(user['TOTAL_POINTS'])
            } for user in users
        }

        with open(JSON_FILE, 'w', encoding='utf-8') as f:
            json.dump(user_data, f, indent=4, ensure_ascii=False)
        logger.info(f"[ JSON ] Saved {len(user_data)} users' points to {JSON_FILE}")
    except Exception as e:
        logger.error(f"[ JSON ] Failed to save user points: {e}")


# ✅ Cập nhật điểm cho user trong file JSON
def update_user_point_in_json(user_id: str, yeumoney_point: float = None, total_point: float = None):
    try:
        if not os.path.exists(JSON_FILE):
            logger.warning("[ JSON ] File not found, creating new one.")
            with open(JSON_FILE, 'w') as f:
                json.dump({}, f)

        with open(JSON_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if user_id not in data:
            data[user_id] = {}

        if yeumoney_point is not None:
            data[user_id]['yeumoney_points'] = float(yeumoney_point)
        if total_point is not None:
            data[user_id]['total_points'] = float(total_point)

        with open(JSON_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)

        logger.info(f"[ JSON ] Updated point(s) for user {user_id}")
    except Exception as e:
        logger.error(f"[ JSON ] Failed to update user {user_id} in JSON: {e}")


# ✅ Ví dụ chạy
if __name__ == "__main__":
    db = DatabaseManager()

    # Ghi toàn bộ user point từ DB xuống JSON
    save_user_points_to_json(db)
