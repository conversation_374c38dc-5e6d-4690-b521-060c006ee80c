from blackboxprotobuf.lib.interface import decode_message

hex_data = input().strip().replace(" ", "")
data = bytes.fromhex(hex_data)

if data[0] != 0x04:
    print("Not a Pomelo data packet")
    exit()

length = int.from_bytes(data[1:4], 'big')
body = data[4:4+length]
flag = body[0]
compress_route = flag & 0x01

idx = 1
if compress_route:
    route_code = int.from_bytes(body[idx:idx+2], 'big')
    idx += 2
    route = f"compressed:{route_code}"
else:
    route_len = body[idx]
    idx += 1
    route = body[idx:idx+route_len].decode('utf-8', errors='replace')
    idx += route_len

payload = body[idx:]

# force typedef
forced_typedef = {'1': {'type': 'int'}}
decoded, typedef = decode_message(payload, forced_typedef)

print(route)
print(decoded)
print(typedef)
